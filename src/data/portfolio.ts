import { PersonalInfo, Project, Education, Experience, Award, Activity, Skill } from '@/types';

export const personalInfo: PersonalInfo = {
  name: "SOUHAIL OUARGUI",
  title: "Software Engineering Student",
  email: "<EMAIL>",
  phone: "+212 608963887",
  location: "Mohammedia, Morocco",
  bio: "I'm a full-stack(MERN) web and mobile developer with a knack for inventing smart IOT solutions. My expertise in both front-end and back-end development allows me to create seamless digital experiences that work across platforms and devices. Let's connect and explore how I can help take your projects to the next level.",
  profileImage: "/images/profile.jpg", // You'll place your photo here
  socialLinks: {
    github: "https://github.com/yourusername",
    linkedin: "https://linkedin.com/in/yourusername",
    twitter: "https://twitter.com/yourusername",
    website: "https://yourwebsite.com"
  }
};

export const projects: Project[] = [
  {
    id: "1",
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.",
    technologies: ["React", "Node.js", "MongoDB", "Express", "Stripe API", "JWT"],
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    liveUrl: "https://your-ecommerce-demo.com",
    imageUrl: "/images/projects/ecommerce.jpg",
    featured: true
  },
  {
    id: "2",
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
    technologies: ["Next.js", "TypeScript", "Prisma", "PostgreSQL", "Socket.io"],
    githubUrl: "https://github.com/yourusername/task-manager",
    liveUrl: "https://your-task-manager.com",
    imageUrl: "/images/projects/task-manager.jpg",
    featured: true
  },
  {
    id: "3",
    title: "Weather Dashboard",
    description: "A responsive weather dashboard that displays current weather conditions and forecasts for multiple cities using external APIs.",
    technologies: ["React", "TypeScript", "OpenWeather API", "Chart.js", "Tailwind CSS"],
    githubUrl: "https://github.com/yourusername/weather-dashboard",
    liveUrl: "https://your-weather-app.com",
    imageUrl: "/images/projects/weather.jpg",
    featured: false
  }
];

export const education: Education[] = [
  {
    id: "1",
    institution: "Your University",
    degree: "Bachelor of Science",
    field: "Software Engineering",
    startDate: "2021",
    endDate: "2025",
    gpa: "3.8/4.0",
    description: "Relevant coursework: Data Structures & Algorithms, Software Design Patterns, Database Systems, Web Development, Mobile App Development, Computer Networks"
  },
  {
    id: "2",
    institution: "Your High School",
    degree: "High School Diploma",
    field: "Science Track",
    startDate: "2017",
    endDate: "2021",
    gpa: "3.9/4.0",
    description: "Graduated with honors. Active in computer science club and mathematics competitions."
  }
];

export const experiences: Experience[] = [
  {
    id: "1",
    company: "Tech Startup Inc.",
    position: "Software Development Intern",
    startDate: "June 2024",
    endDate: "August 2024",
    description: "Developed and maintained web applications using React and Node.js. Collaborated with senior developers on feature implementation and bug fixes. Participated in code reviews and agile development processes.",
    technologies: ["React", "Node.js", "PostgreSQL", "Git", "Docker"],
    type: "internship"
  },
  {
    id: "2",
    company: "University IT Department",
    position: "Student Developer",
    startDate: "September 2023",
    endDate: "Present",
    description: "Part-time position developing internal tools and maintaining university websites. Responsible for frontend development and user experience improvements.",
    technologies: ["Vue.js", "PHP", "MySQL", "WordPress"],
    type: "part-time"
  }
];

export const awards: Award[] = [
  {
    id: "1",
    title: "Dean's List",
    organization: "Your University",
    date: "Fall 2023",
    description: "Achieved Dean's List recognition for academic excellence with a GPA of 3.8 or higher."
  },
  {
    id: "2",
    title: "Best Innovation Award",
    organization: "University Hackathon 2024",
    date: "March 2024",
    description: "Won first place for developing an AI-powered study assistant application during the 48-hour hackathon."
  },
  {
    id: "3",
    title: "Programming Competition Winner",
    organization: "Regional Coding Contest",
    date: "November 2023",
    description: "Placed 1st in regional programming competition, solving complex algorithmic problems."
  }
];

export const activities: Activity[] = [
  {
    id: "1",
    title: "Computer Science Club",
    organization: "Your University",
    role: "Vice President",
    startDate: "September 2023",
    endDate: "Present",
    description: "Organize coding workshops, tech talks, and networking events. Mentor junior students in programming and career development."
  },
  {
    id: "2",
    title: "Open Source Contributor",
    organization: "Various Projects",
    role: "Contributor",
    startDate: "January 2023",
    endDate: "Present",
    description: "Active contributor to open source projects on GitHub. Focus on web development tools and educational resources."
  },
  {
    id: "3",
    title: "Volunteer Coding Instructor",
    organization: "Local Community Center",
    role: "Instructor",
    startDate: "June 2023",
    endDate: "Present",
    description: "Teach basic programming concepts to high school students during summer programs."
  }
];

export const skills: Skill[] = [
  // Frontend
  { name: "React", category: "frontend", proficiency: "advanced" },
  { name: "Next.js", category: "frontend", proficiency: "intermediate" },
  { name: "TypeScript", category: "frontend", proficiency: "intermediate" },
  { name: "JavaScript", category: "frontend", proficiency: "advanced" },
  { name: "HTML/CSS", category: "frontend", proficiency: "advanced" },
  { name: "Tailwind CSS", category: "frontend", proficiency: "intermediate" },
  { name: "Vue.js", category: "frontend", proficiency: "intermediate" },
  
  // Backend
  { name: "Node.js", category: "backend", proficiency: "intermediate" },
  { name: "Express.js", category: "backend", proficiency: "intermediate" },
  { name: "Python", category: "backend", proficiency: "intermediate" },
  { name: "Java", category: "backend", proficiency: "intermediate" },
  { name: "PHP", category: "backend", proficiency: "beginner" },
  
  // Database
  { name: "MongoDB", category: "database", proficiency: "intermediate" },
  { name: "PostgreSQL", category: "database", proficiency: "intermediate" },
  { name: "MySQL", category: "database", proficiency: "intermediate" },
  
  // Tools
  { name: "Git", category: "tools", proficiency: "advanced" },
  { name: "Docker", category: "tools", proficiency: "beginner" },
  { name: "VS Code", category: "tools", proficiency: "advanced" },
  { name: "Figma", category: "tools", proficiency: "intermediate" },
  
  // Languages
  { name: "English", category: "languages", proficiency: "expert" },
  { name: "French", category: "languages", proficiency: "intermediate" },
  { name: "Spanish", category: "languages", proficiency: "beginner" }
];
