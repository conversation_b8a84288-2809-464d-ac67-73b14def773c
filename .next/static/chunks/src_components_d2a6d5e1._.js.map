{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaBars, FaTimes, FaMoon, FaSun } from 'react-icons/fa';\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('');\n  const [isDark, setIsDark] = useState(false);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Education', href: '#education' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Awards', href: '#awards-activities' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    } else {\n      setIsDark(false);\n      document.documentElement.classList.remove('dark');\n    }\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = navItems.map(item => item.href.substring(1));\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const offsetTop = element.offsetTop;\n          const offsetHeight = element.offsetHeight;\n\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Call once to set initial active section\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.getElementById(href.substring(1));\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsOpen(false);\n  };\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className=\"fixed top-0 left-0 right-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md border-b border-gray-200 dark:border-gray-700\"\n    >\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-xl font-bold text-gray-900 dark:text-white\"\n            whileHover={{ scale: 1.05 }}\n          >\n            Portfolio\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.button\n                key={item.name}\n                onClick={() => scrollToSection(item.href)}\n                className={`text-sm font-medium transition-colors ${\n                  activeSection === item.href.substring(1)\n                    ? 'text-indigo-600 dark:text-indigo-400'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400'\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {item.name}\n              </motion.button>\n            ))}\n            \n            {/* Theme Toggle */}\n            <motion.button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isDark ? <FaSun size={18} /> : <FaMoon size={18} />}\n            </motion.button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center gap-4\">\n            <motion.button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isDark ? <FaSun size={18} /> : <FaMoon size={18} />}\n            </motion.button>\n            \n            <motion.button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isOpen ? <FaTimes size={20} /> : <FaBars size={20} />}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden border-t border-gray-200 dark:border-gray-700\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item) => (\n                  <motion.button\n                    key={item.name}\n                    onClick={() => scrollToSection(item.href)}\n                    className={`block w-full text-left px-4 py-2 text-sm font-medium transition-colors ${\n                      activeSection === item.href.substring(1)\n                        ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30'\n                        : 'text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-50 dark:hover:bg-gray-800'\n                    }`}\n                    whileHover={{ x: 5 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {item.name}\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAU,MAAM;QAAqB;QAC7C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,4DAA4D;YAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAE7E,IAAI,eAAe,UAAW,CAAC,cAAc,aAAc;gBACzD,UAAU;gBACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,UAAU;gBACV,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;QACF;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,MAAM,WAAW,SAAS,GAAG;sEAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;;oBAC1D,MAAM,iBAAiB,OAAO,OAAO,GAAG;oBAExC,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,SAAS;4BACX,MAAM,YAAY,QAAQ,SAAS;4BACnC,MAAM,eAAe,QAAQ,YAAY;4BAEzC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;gCAC5E,iBAAiB;gCACjB;4BACF;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC,gBAAgB,0CAA0C;YAE1D;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;QACvD,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,UAAU;IACZ;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;sCAC3B;;;;;;sCAKD,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAW,CAAC,sCAAsC,EAChD,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,yCACA,qFACJ;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAEvB,KAAK,IAAI;uCAVL,KAAK,IAAI;;;;;8CAelB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,6LAAC,iJAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,6LAAC,iJAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAGhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,UAAU,CAAC;oCAC1B,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,6LAAC,iJAAA,CAAA,UAAO;wCAAC,MAAM;;;;;6DAAS,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMtD,6LAAC,4LAAA,CAAA,kBAAe;8BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAW,CAAC,uEAAuE,EACjF,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,4EACA,6HACJ;oCACF,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,IAAI;mCAVL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBlC;GA/KwB;KAAA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaGlobe, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface HeroProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Hero({ personalInfo }: HeroProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 px-4\">\n      <motion.div\n        className=\"max-w-4xl mx-auto text-center\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <motion.div\n          variants={itemVariants}\n          className=\"mb-8\"\n        >\n          {personalInfo.profileImage && (\n            <div className=\"w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-white shadow-lg\">\n              <img\n                src={personalInfo.profileImage}\n                alt={personalInfo.name}\n                className=\"w-full h-full object-cover\"\n                onError={(e) => {\n                  // Fallback to a placeholder if image fails to load\n                  e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(personalInfo.name)}&size=128&background=6366f1&color=ffffff`;\n                }}\n              />\n            </div>\n          )}\n        </motion.div>\n\n        <motion.h1\n          variants={itemVariants}\n          className=\"text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4\"\n        >\n          {personalInfo.name}\n        </motion.h1>\n\n        <motion.h2\n          variants={itemVariants}\n          className=\"text-xl md:text-2xl text-indigo-600 dark:text-indigo-400 mb-6 font-medium\"\n        >\n          {personalInfo.title}\n        </motion.h2>\n\n        <motion.p\n          variants={itemVariants}\n          className=\"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed\"\n        >\n          {personalInfo.bio}\n        </motion.p>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"flex flex-wrap justify-center gap-4 mb-8 text-sm text-gray-600 dark:text-gray-300\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <FaEnvelope className=\"text-indigo-600 dark:text-indigo-400\" />\n            <a href={`mailto:${personalInfo.email}`} className=\"hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\">\n              {personalInfo.email}\n            </a>\n          </div>\n          {personalInfo.phone && (\n            <div className=\"flex items-center gap-2\">\n              <FaPhone className=\"text-indigo-600 dark:text-indigo-400\" />\n              <a href={`tel:${personalInfo.phone}`} className=\"hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\">\n                {personalInfo.phone}\n              </a>\n            </div>\n          )}\n          <div className=\"flex items-center gap-2\">\n            <FaMapMarkerAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n            <span>{personalInfo.location}</span>\n          </div>\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"flex justify-center gap-6\"\n        >\n          {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n            if (!url) return null;\n            const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n            if (!IconComponent) return null;\n\n            return (\n              <motion.a\n                key={platform}\n                href={url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-2xl text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                whileHover={{ scale: 1.2 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <IconComponent />\n              </motion.a>\n            );\n          })}\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"mt-12\"\n        >\n          <motion.button\n            onClick={() => {\n              const aboutSection = document.getElementById('about');\n              aboutSection?.scrollIntoView({ behavior: 'smooth' });\n            }}\n            className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-full font-medium transition-colors shadow-lg hover:shadow-xl\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Learn More About Me\n          </motion.button>\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,KAAK,EAAE,YAAY,EAAa;IACtD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ,iJAAA,CAAA,WAAQ;QAChB,UAAU,iJAAA,CAAA,aAAU;QACpB,SAAS,iJAAA,CAAA,YAAS;QAClB,SAAS,iJAAA,CAAA,UAAO;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,UAAU;YACV,SAAQ;YACR,SAAQ;;8BAER,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,aAAa,YAAY,kBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK,aAAa,YAAY;4BAC9B,KAAK,aAAa,IAAI;4BACtB,WAAU;4BACV,SAAS,CAAC;gCACR,mDAAmD;gCACnD,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,iCAAiC,EAAE,mBAAmB,aAAa,IAAI,EAAE,wCAAwC,CAAC;4BAC3I;;;;;;;;;;;;;;;;8BAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,UAAU;oBACV,WAAU;8BAET,aAAa,IAAI;;;;;;8BAGpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,UAAU;oBACV,WAAU;8BAET,aAAa,KAAK;;;;;;8BAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,UAAU;oBACV,WAAU;8BAET,aAAa,GAAG;;;;;;8BAGnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAE,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oCAAE,WAAU;8CAChD,aAAa,KAAK;;;;;;;;;;;;wBAGtB,aAAa,KAAK,kBACjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;oCAAE,WAAU;8CAC7C,aAAa,KAAK;;;;;;;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;8CAC1B,6LAAC;8CAAM,aAAa,QAAQ;;;;;;;;;;;;;;;;;;8BAIhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wBAC5D,IAAI,CAAC,KAAK,OAAO;wBACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wBACvE,IAAI,CAAC,eAAe,OAAO;wBAE3B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BAEP,MAAM;4BACN,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEvB,cAAA,6LAAC;;;;;2BARI;;;;;oBAWX;;;;;;8BAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BACP,MAAM,eAAe,SAAS,cAAc,CAAC;4BAC7C,cAAc,eAAe;gCAAE,UAAU;4BAAS;wBACpD;wBACA,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;;;;;AAOX;KAhJwB", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { PersonalInfo } from '@/types';\n\ninterface AboutProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function About({ personalInfo }: AboutProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              About Me\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div variants={itemVariants}>\n              <div className=\"space-y-6\">\n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  {personalInfo.bio}\n                </p>\n                \n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  I'm passionate about creating efficient, scalable, and user-friendly applications. \n                  My journey in software engineering has equipped me with a strong foundation in \n                  various programming languages and frameworks, and I'm always eager to learn \n                  new technologies and tackle challenging problems.\n                </p>\n\n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  When I'm not coding, you can find me contributing to open source projects, \n                  participating in hackathons, or mentoring fellow students. I believe in the \n                  power of technology to make a positive impact on the world.\n                </p>\n\n                <div className=\"flex flex-wrap gap-4 mt-8\">\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Problem Solver</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Team Player</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Quick Learner</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Creative Thinker</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 p-8 rounded-2xl\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Quick Facts\n                </h3>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Location</span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{personalInfo.location}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Current Role</span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{personalInfo.title}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Email</span>\n                    <a \n                      href={`mailto:${personalInfo.email}`}\n                      className=\"font-medium text-indigo-600 dark:text-indigo-400 hover:underline\"\n                    >\n                      {personalInfo.email}\n                    </a>\n                  </div>\n                  \n                  {personalInfo.phone && (\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-gray-600 dark:text-gray-300\">Phone</span>\n                      <a \n                        href={`tel:${personalInfo.phone}`}\n                        className=\"font-medium text-indigo-600 dark:text-indigo-400 hover:underline\"\n                      >\n                        {personalInfo.phone}\n                      </a>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mt-8\">\n                  <a\n                    href=\"/resume.pdf\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center justify-center w-full bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n                  >\n                    Download Resume\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,MAAM,EAAE,YAAY,EAAc;IACxD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,aAAa,GAAG;;;;;;sDAGnB,6LAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAOxE,6LAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAMxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAK,WAAU;sEAA6C,aAAa,QAAQ;;;;;;;;;;;;8DAGpF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAK,WAAU;sEAA6C,aAAa,KAAK;;;;;;;;;;;;8DAGjF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;4DACpC,WAAU;sEAET,aAAa,KAAK;;;;;;;;;;;;gDAItB,aAAa,KAAK,kBACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;4DACjC,WAAU;sEAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;sDAM3B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;KAnIwB", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Skill } from '@/types';\n\ninterface SkillsProps {\n  skills: Skill[];\n}\n\nexport default function Skills({ skills }: SkillsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const skillCategories = {\n    frontend: 'Frontend',\n    backend: 'Backend',\n    database: 'Database',\n    tools: 'Tools & Technologies',\n    languages: 'Languages',\n    other: 'Other'\n  };\n\n  const proficiencyColors = {\n    beginner: 'bg-yellow-200 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n    intermediate: 'bg-blue-200 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n    advanced: 'bg-green-200 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n    expert: 'bg-purple-200 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'\n  };\n\n  const groupedSkills = skills.reduce((acc, skill) => {\n    if (!acc[skill.category]) {\n      acc[skill.category] = [];\n    }\n    acc[skill.category].push(skill);\n    return acc;\n  }, {} as Record<string, Skill[]>);\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Skills & Technologies\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Here are the technologies and tools I work with to bring ideas to life.\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {Object.entries(groupedSkills).map(([category, categorySkills]) => (\n              <motion.div\n                key={category}\n                variants={itemVariants}\n                className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n              >\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n                  {skillCategories[category as keyof typeof skillCategories]}\n                </h3>\n                \n                <div className=\"space-y-3\">\n                  {categorySkills.map((skill, index) => (\n                    <motion.div\n                      key={skill.name}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                      className=\"flex items-center justify-between\"\n                    >\n                      <span className=\"text-gray-700 dark:text-gray-300 font-medium\">\n                        {skill.name}\n                      </span>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${proficiencyColors[skill.proficiency]}`}>\n                        {skill.proficiency}\n                      </span>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                Proficiency Legend\n              </h3>\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                {Object.entries(proficiencyColors).map(([level, colorClass]) => (\n                  <div key={level} className=\"flex items-center gap-2\">\n                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}>\n                      {level}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,OAAO,EAAE,MAAM,EAAe;IACpD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,WAAW;QACX,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,UAAU;QACV,cAAc;QACd,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,KAAK;QACxC,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,EAAE;YACxB,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE;QAC1B;QACA,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,IAAI,CAAC;QACzB,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,6LAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,eAAe,iBAC5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDACX,eAAe,CAAC,SAAyC;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEACb,MAAM,IAAI;;;;;;kEAEb,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,CAAC,MAAM,WAAW,CAAC,EAAE;kEAClG,MAAM,WAAW;;;;;;;+CAVf,MAAM,IAAI;;;;;;;;;;;+BAXhB;;;;;;;;;;kCA8BX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,OAAO,WAAW,iBACzD,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY;0DACxE;;;;;;2CAFK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa5B;KAxHwB", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaStar } from 'react-icons/fa';\nimport { Project } from '@/types';\n\ninterface ProjectsProps {\n  projects: Project[];\n}\n\nexport default function Projects({ projects }: ProjectsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const featuredProjects = projects.filter(project => project.featured);\n  const otherProjects = projects.filter(project => !project.featured);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Featured Projects\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Here are some of the projects I've worked on that showcase my skills and passion for development.\n            </p>\n          </motion.div>\n\n          {/* Featured Projects */}\n          <div className=\"space-y-12 mb-16\">\n            {featuredProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                variants={itemVariants}\n                className={`grid md:grid-cols-2 gap-8 items-center ${\n                  index % 2 === 1 ? 'md:grid-flow-col-dense' : ''\n                }`}\n              >\n                <div className={`${index % 2 === 1 ? 'md:col-start-2' : ''}`}>\n                  <div className=\"relative group\">\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200\"></div>\n                    <div className=\"relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden aspect-video\">\n                      {project.imageUrl ? (\n                        <img\n                          src={project.imageUrl}\n                          alt={project.title}\n                          className=\"w-full h-full object-cover\"\n                          onError={(e) => {\n                            e.currentTarget.src = `https://via.placeholder.com/600x400/6366f1/ffffff?text=${encodeURIComponent(project.title)}`;\n                          }}\n                        />\n                      ) : (\n                        <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600\">\n                          <span className=\"text-white text-2xl font-bold\">{project.title}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                <div className={`${index % 2 === 1 ? 'md:col-start-1 md:row-start-1' : ''}`}>\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <FaStar className=\"text-yellow-500\" />\n                    <span className=\"text-sm font-medium text-indigo-600 dark:text-indigo-400\">\n                      Featured Project\n                    </span>\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                    {project.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\">\n                    {project.description}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-2 mb-6\">\n                    {project.technologies.map((tech) => (\n                      <span\n                        key={tech}\n                        className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-sm font-medium\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex gap-4\">\n                    {project.githubUrl && (\n                      <motion.a\n                        href={project.githubUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <FaGithub />\n                        <span>Code</span>\n                      </motion.a>\n                    )}\n                    {project.liveUrl && (\n                      <motion.a\n                        href={project.liveUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <FaExternalLinkAlt />\n                        <span>Live Demo</span>\n                      </motion.a>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Other Projects */}\n          {otherProjects.length > 0 && (\n            <>\n              <motion.div variants={itemVariants} className=\"text-center mb-12\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                  Other Projects\n                </h3>\n              </motion.div>\n\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {otherProjects.map((project) => (\n                  <motion.div\n                    key={project.id}\n                    variants={itemVariants}\n                    className=\"bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow\"\n                    whileHover={{ y: -5 }}\n                  >\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                      {project.title}\n                    </h4>\n                    \n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed\">\n                      {project.description}\n                    </p>\n                    \n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {project.technologies.slice(0, 3).map((tech) => (\n                        <span\n                          key={tech}\n                          className=\"px-2 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded text-xs font-medium\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                      {project.technologies.length > 3 && (\n                        <span className=\"px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs\">\n                          +{project.technologies.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex gap-4\">\n                      {project.githubUrl && (\n                        <a\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          <FaGithub size={18} />\n                        </a>\n                      )}\n                      {project.liveUrl && (\n                        <a\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          <FaExternalLinkAlt size={16} />\n                        </a>\n                      )}\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,SAAS,EAAE,QAAQ,EAAiB;IAC1D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ;IAElE,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAM5E,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAW,CAAC,uCAAuC,EACjD,QAAQ,MAAM,IAAI,2BAA2B,IAC7C;;kDAEF,6LAAC;wCAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,mBAAmB,IAAI;kDAC1D,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,iBACf,6LAAC;wDACC,KAAK,QAAQ,QAAQ;wDACrB,KAAK,QAAQ,KAAK;wDAClB,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,KAAK,GAAG;wDACrH;;;;;6EAGF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAiC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxE,6LAAC;wCAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,kCAAkC,IAAI;;0DACzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAK7E,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DAQX,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,SAAS,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAM,QAAQ,SAAS;wDACvB,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,6LAAC,iJAAA,CAAA,WAAQ;;;;;0EACT,6LAAC;0EAAK;;;;;;;;;;;;oDAGT,QAAQ,OAAO,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAM,QAAQ,OAAO;wDACrB,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,6LAAC,iJAAA,CAAA,oBAAiB;;;;;0EAClB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;+BA/ET,QAAQ,EAAE;;;;;;;;;;oBAyFpB,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;0CAC5C,cAAA,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;;;;;;0CAKxE,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;wCACV,YAAY;4CAAE,GAAG,CAAC;wCAAE;;0DAEpB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,6LAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;oDAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,6LAAC;wDAAK,WAAU;;4DAA0F;4DACtG,QAAQ,YAAY,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;0DAKxC,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,SAAS,kBAChB,6LAAC;wDACC,MAAM,QAAQ,SAAS;wDACvB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAEV,cAAA,6LAAC,iJAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;;;;;;oDAGnB,QAAQ,OAAO,kBACd,6LAAC;wDACC,MAAM,QAAQ,OAAO;wDACrB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAEV,cAAA,6LAAC,iJAAA,CAAA,oBAAiB;4DAAC,MAAM;;;;;;;;;;;;;;;;;;uCA/C1B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DnC;KA/MwB", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Education.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGraduationCap, FaCalendarAlt, FaStar } from 'react-icons/fa';\nimport { Education } from '@/types';\n\ninterface EducationProps {\n  education: Education[];\n}\n\nexport default function Education({ education }: EducationProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"education\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Education\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              My academic journey and the foundation of my technical knowledge.\n            </p>\n          </motion.div>\n\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-indigo-200 dark:bg-indigo-800 hidden md:block\"></div>\n\n            <div className=\"space-y-12\">\n              {education.map((edu, index) => (\n                <motion.div\n                  key={edu.id}\n                  variants={itemVariants}\n                  className=\"relative\"\n                >\n                  {/* Timeline dot */}\n                  <div className=\"absolute left-6 w-4 h-4 bg-indigo-600 rounded-full border-4 border-white dark:border-gray-800 hidden md:block\"></div>\n\n                  <div className=\"md:ml-20\">\n                    <motion.div\n                      className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\n                      whileHover={{ y: -5 }}\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-start md:justify-between mb-4\">\n                        <div className=\"flex items-center gap-3 mb-2 md:mb-0\">\n                          <FaGraduationCap className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                            {edu.degree} in {edu.field}\n                          </h3>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                          <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                          <span>{edu.startDate} - {edu.endDate}</span>\n                        </div>\n                      </div>\n\n                      <h4 className=\"text-lg font-semibold text-indigo-600 dark:text-indigo-400 mb-4\">\n                        {edu.institution}\n                      </h4>\n\n                      {edu.gpa && (\n                        <div className=\"flex items-center gap-2 mb-4\">\n                          <FaStar className=\"text-yellow-500\" />\n                          <span className=\"text-gray-700 dark:text-gray-300\">\n                            GPA: <span className=\"font-semibold\">{edu.gpa}</span>\n                          </span>\n                        </div>\n                      )}\n\n                      {edu.description && (\n                        <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed\">\n                          {edu.description}\n                        </p>\n                      )}\n\n                      {/* Progress indicator for current education */}\n                      {edu.endDate.toLowerCase().includes('present') || \n                       edu.endDate.toLowerCase().includes('current') || \n                       parseInt(edu.endDate) > new Date().getFullYear() && (\n                        <div className=\"mt-4\">\n                          <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                            <span>Progress</span>\n                            <span>In Progress</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <motion.div\n                              className=\"bg-indigo-600 h-2 rounded-full\"\n                              initial={{ width: 0 }}\n                              whileInView={{ width: '75%' }}\n                              transition={{ duration: 1, delay: 0.5 }}\n                            ></motion.div>\n                          </div>\n                        </div>\n                      )}\n                    </motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Additional achievements or certifications section */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                Academic Achievements\n              </h3>\n              \n              <div className=\"grid md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaGraduationCap className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Dean's List</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Multiple semesters of academic excellence\n                  </p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaStar className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Honor Society</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Member of Computer Science Honor Society\n                  </p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaCalendarAlt className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Expected Graduation</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    On track for timely completion\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,UAAU,EAAE,SAAS,EAAkB;IAC7D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAY,WAAU;kBAChC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG,CAAC;oDAAE;;sEAEpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,iJAAA,CAAA,kBAAe;4EAAC,WAAU;;;;;;sFAC3B,6LAAC;4EAAG,WAAU;;gFACX,IAAI,MAAM;gFAAC;gFAAK,IAAI,KAAK;;;;;;;;;;;;;8EAI9B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,iJAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;sFACzB,6LAAC;;gFAAM,IAAI,SAAS;gFAAC;gFAAI,IAAI,OAAO;;;;;;;;;;;;;;;;;;;sEAIxC,6LAAC;4DAAG,WAAU;sEACX,IAAI,WAAW;;;;;;wDAGjB,IAAI,GAAG,kBACN,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iJAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;;wEAAmC;sFAC5C,6LAAC;4EAAK,WAAU;sFAAiB,IAAI,GAAG;;;;;;;;;;;;;;;;;;wDAKlD,IAAI,WAAW,kBACd,6LAAC;4DAAE,WAAU;sEACV,IAAI,WAAW;;;;;;wDAKnB,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,SAAS,IAAI,OAAO,IAAI,IAAI,OAAO,WAAW,oBAC7C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;sFAAK;;;;;;;;;;;;8EAER,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,aAAa;4EAAE,OAAO;wEAAM;wEAC5B,YAAY;4EAAE,UAAU;4EAAG,OAAO;wEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3D7C,IAAI,EAAE;;;;;;;;;;;;;;;;kCAwEnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAItE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;8DAE7B,6LAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAK1D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAK1D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E;KArKwB", "debugId": null}}, {"offset": {"line": 2172, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Experience.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaBriefcase, FaCalendarAlt, FaMapMarkerAlt } from 'react-icons/fa';\nimport { Experience } from '@/types';\n\ninterface ExperienceProps {\n  experiences: Experience[];\n}\n\nexport default function Experience({ experiences }: ExperienceProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'internship':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'full-time':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';\n      case 'part-time':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'contract':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getTypeLabel = (type: string) => {\n    return type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ');\n  };\n\n  return (\n    <section id=\"experience\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Work Experience\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              My professional journey and the hands-on experience I've gained in the industry.\n            </p>\n          </motion.div>\n\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-indigo-200 dark:bg-indigo-800 hidden md:block\"></div>\n\n            <div className=\"space-y-12\">\n              {experiences.map((exp, index) => (\n                <motion.div\n                  key={exp.id}\n                  variants={itemVariants}\n                  className=\"relative\"\n                >\n                  {/* Timeline dot */}\n                  <div className=\"absolute left-6 w-4 h-4 bg-indigo-600 rounded-full border-4 border-white dark:border-gray-900 hidden md:block\"></div>\n\n                  <div className=\"md:ml-20\">\n                    <motion.div\n                      className=\"bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\n                      whileHover={{ y: -5 }}\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-start md:justify-between mb-4\">\n                        <div className=\"flex items-center gap-3 mb-2 md:mb-0\">\n                          <FaBriefcase className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                          <div>\n                            <h3 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                              {exp.position}\n                            </h3>\n                            <h4 className=\"text-lg font-semibold text-indigo-600 dark:text-indigo-400\">\n                              {exp.company}\n                            </h4>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex flex-col gap-2\">\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{exp.startDate} - {exp.endDate}</span>\n                          </div>\n                          \n                          <span className={`px-3 py-1 rounded-full text-xs font-medium self-start ${getTypeColor(exp.type)}`}>\n                            {getTypeLabel(exp.type)}\n                          </span>\n                        </div>\n                      </div>\n\n                      <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed mb-6\">\n                        {exp.description}\n                      </p>\n\n                      <div className=\"space-y-4\">\n                        <div>\n                          <h5 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-2\">\n                            Technologies Used:\n                          </h5>\n                          <div className=\"flex flex-wrap gap-2\">\n                            {exp.technologies.map((tech) => (\n                              <span\n                                key={tech}\n                                className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-sm font-medium\"\n                              >\n                                {tech}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Current position indicator */}\n                        {(exp.endDate.toLowerCase().includes('present') || \n                          exp.endDate.toLowerCase().includes('current')) && (\n                          <div className=\"flex items-center gap-2 text-sm\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                            <span className=\"text-green-600 dark:text-green-400 font-medium\">\n                              Currently working here\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Call to action for more opportunities */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                Looking for New Opportunities\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto\">\n                I'm always interested in new challenges and opportunities to grow. \n                If you have an exciting project or position, I'd love to hear from you!\n              </p>\n              <motion.a\n                href=\"#contact\"\n                className=\"inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg font-medium transition-colors\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={(e) => {\n                  e.preventDefault();\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Get In Touch\n              </motion.a>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,WAAW,EAAE,WAAW,EAAmB;IACjE,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;IACnE;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DAEf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG,CAAC;oDAAE;;sEAEpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,iJAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,IAAI,QAAQ;;;;;;8FAEf,6LAAC;oFAAG,WAAU;8FACX,IAAI,OAAO;;;;;;;;;;;;;;;;;;8EAKlB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,iJAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,6LAAC;;wFAAM,IAAI,SAAS;wFAAC;wFAAI,IAAI,OAAO;;;;;;;;;;;;;sFAGtC,6LAAC;4EAAK,WAAW,CAAC,sDAAsD,EAAE,aAAa,IAAI,IAAI,GAAG;sFAC/F,aAAa,IAAI,IAAI;;;;;;;;;;;;;;;;;;sEAK5B,6LAAC;4DAAE,WAAU;sEACV,IAAI,WAAW;;;;;;sEAGlB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA2D;;;;;;sFAGzE,6LAAC;4EAAI,WAAU;sFACZ,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;;;;;;gEAUZ,CAAC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,mBAC7C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA/DtE,IAAI,EAAE;;;;;;;;;;;;;;;;kCA6EnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,6LAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAIvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,gBAAgB,eAAe;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KA9KwB", "debugId": null}}, {"offset": {"line": 2580, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/AwardsActivities.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaTrophy, FaUsers, FaCalendarAlt, FaMedal, FaHandsHelping } from 'react-icons/fa';\nimport { Award, Activity } from '@/types';\n\ninterface AwardsActivitiesProps {\n  awards: Award[];\n  activities: Activity[];\n}\n\nexport default function AwardsActivities({ awards, activities }: AwardsActivitiesProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"awards-activities\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Awards & Activities\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Recognition for my achievements and involvement in extracurricular activities.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Awards Section */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <FaTrophy className=\"text-2xl text-yellow-500\" />\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Awards & Recognition\n                </h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {awards.map((award, index) => (\n                  <motion.div\n                    key={award.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n                    whileHover={{ y: -3 }}\n                  >\n                    <div className=\"flex items-start gap-4\">\n                      <div className=\"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <FaMedal className=\"text-yellow-600 dark:text-yellow-400 text-xl\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\n                          <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                            {award.title}\n                          </h4>\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{award.date}</span>\n                          </div>\n                        </div>\n                        \n                        <p className=\"text-indigo-600 dark:text-indigo-400 font-medium mb-2\">\n                          {award.organization}\n                        </p>\n                        \n                        <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                          {award.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Activities Section */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <FaUsers className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Extracurricular Activities\n                </h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {activities.map((activity, index) => (\n                  <motion.div\n                    key={activity.id}\n                    initial={{ opacity: 0, x: 20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n                    whileHover={{ y: -3 }}\n                  >\n                    <div className=\"flex items-start gap-4\">\n                      <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <FaHandsHelping className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\n                          <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                            {activity.title}\n                          </h4>\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{activity.startDate} - {activity.endDate || 'Present'}</span>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex flex-col md:flex-row md:items-center gap-2 mb-3\">\n                          <p className=\"text-indigo-600 dark:text-indigo-400 font-medium\">\n                            {activity.organization}\n                          </p>\n                          <span className=\"hidden md:block text-gray-400\">•</span>\n                          <span className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-xs font-medium\">\n                            {activity.role}\n                          </span>\n                        </div>\n                        \n                        <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                          {activity.description}\n                        </p>\n\n                        {/* Current activity indicator */}\n                        {(!activity.endDate || \n                          activity.endDate.toLowerCase().includes('present') || \n                          activity.endDate.toLowerCase().includes('current')) && (\n                          <div className=\"flex items-center gap-2 text-sm mt-3\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                            <span className=\"text-green-600 dark:text-green-400 font-medium\">\n                              Currently active\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Summary Stats */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white text-center mb-8\">\n                Achievement Summary\n              </h3>\n              \n              <div className=\"grid md:grid-cols-4 gap-6 text-center\">\n                <div>\n                  <div className=\"w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaTrophy className=\"text-2xl text-yellow-600 dark:text-yellow-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {awards.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Awards Received\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaUsers className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Active Involvements\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaHandsHelping className=\"text-2xl text-green-600 dark:text-green-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.filter(a => a.role.toLowerCase().includes('volunteer') || \n                                           a.title.toLowerCase().includes('volunteer')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Volunteer Roles\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaMedal className=\"text-2xl text-purple-600 dark:text-purple-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.filter(a => a.role.toLowerCase().includes('president') || \n                                           a.role.toLowerCase().includes('leader')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Leadership Positions\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,iBAAiB,EAAE,MAAM,EAAE,UAAU,EAAyB;IACpF,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;kDAKnE,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;gDACV,YAAY;oDAAE,GAAG,CAAC;gDAAE;0DAEpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAGrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,MAAM,KAAK;;;;;;sFAEd,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,iJAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,6LAAC;8FAAM,MAAM,IAAI;;;;;;;;;;;;;;;;;;8EAIrB,6LAAC;oEAAE,WAAU;8EACV,MAAM,YAAY;;;;;;8EAGrB,6LAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;+CA5BnB,MAAM,EAAE;;;;;;;;;;;;;;;;0CAsCrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;kDAKnE,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;gDACV,YAAY;oDAAE,GAAG,CAAC;gDAAE;0DAEpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAG5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,SAAS,KAAK;;;;;;sFAEjB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,iJAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,6LAAC;;wFAAM,SAAS,SAAS;wFAAC;wFAAI,SAAS,OAAO,IAAI;;;;;;;;;;;;;;;;;;;8EAItD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,SAAS,YAAY;;;;;;sFAExB,6LAAC;4EAAK,WAAU;sFAAgC;;;;;;sFAChD,6LAAC;4EAAK,WAAU;sFACb,SAAS,IAAI;;;;;;;;;;;;8EAIlB,6LAAC;oEAAE,WAAU;8EACV,SAAS,WAAW;;;;;;gEAItB,CAAC,CAAC,SAAS,OAAO,IACjB,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACxC,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,mBAClD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAiD;;;;;;;;;;;;;;;;;;;;;;;;+CA3CpE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAyD1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAIlF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM;;;;;;8DAEhB,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC/B,EAAE,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,MAAM;;;;;;8DAE5E,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC/B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,MAAM;;;;;;8DAExE,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E;KAjOwB", "debugId": null}}, {"offset": {"line": 3238, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaGithub, FaLinkedin, FaTwitter, FaGlobe } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface ContactProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Contact({ personalInfo }: ContactProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Get In Touch\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              I'm always open to discussing new opportunities, collaborations, or just having a chat about technology.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Information */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Contact Information\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  <motion.div\n                    className=\"flex items-center gap-4\"\n                    whileHover={{ x: 5 }}\n                  >\n                    <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                      <FaEnvelope className=\"text-indigo-600 dark:text-indigo-400\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-300\">Email</p>\n                      <a\n                        href={`mailto:${personalInfo.email}`}\n                        className=\"text-gray-900 dark:text-white font-medium hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                      >\n                        {personalInfo.email}\n                      </a>\n                    </div>\n                  </motion.div>\n\n                  {personalInfo.phone && (\n                    <motion.div\n                      className=\"flex items-center gap-4\"\n                      whileHover={{ x: 5 }}\n                    >\n                      <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                        <FaPhone className=\"text-indigo-600 dark:text-indigo-400\" />\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-300\">Phone</p>\n                        <a\n                          href={`tel:${personalInfo.phone}`}\n                          className=\"text-gray-900 dark:text-white font-medium hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          {personalInfo.phone}\n                        </a>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <motion.div\n                    className=\"flex items-center gap-4\"\n                    whileHover={{ x: 5 }}\n                  >\n                    <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                      <FaMapMarkerAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-300\">Location</p>\n                      <p className=\"text-gray-900 dark:text-white font-medium\">\n                        {personalInfo.location}\n                      </p>\n                    </div>\n                  </motion.div>\n                </div>\n\n                {/* Social Links */}\n                <div className=\"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700\">\n                  <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                    Connect with me\n                  </h4>\n                  <div className=\"flex gap-4\">\n                    {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n                      if (!url) return null;\n                      const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n                      if (!IconComponent) return null;\n\n                      return (\n                        <motion.a\n                          key={platform}\n                          href={url}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-indigo-600 hover:text-white transition-colors\"\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.9 }}\n                        >\n                          <IconComponent size={20} />\n                        </motion.a>\n                      );\n                    })}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Send me a message\n                </h3>\n                \n                <form className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                        placeholder=\"Your name\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Subject\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                      placeholder=\"What's this about?\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      rows={6}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none\"\n                      placeholder=\"Tell me about your project or opportunity...\"\n                    ></textarea>\n                  </div>\n                  \n                  <motion.button\n                    type=\"submit\"\n                    className=\"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg font-medium transition-colors\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    Send Message\n                  </motion.button>\n                </form>\n                \n                <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-4 text-center\">\n                  Or email me directly at{' '}\n                  <a\n                    href={`mailto:${personalInfo.email}`}\n                    className=\"text-indigo-600 dark:text-indigo-400 hover:underline\"\n                  >\n                    {personalInfo.email}\n                  </a>\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,QAAQ,EAAE,YAAY,EAAgB;IAC5D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ,iJAAA,CAAA,WAAQ;QAChB,UAAU,iJAAA,CAAA,aAAU;QACpB,SAAS,iJAAA,CAAA,YAAS;QAClB,SAAS,iJAAA,CAAA,UAAO;IAClB;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oEACpC,WAAU;8EAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;gDAKxB,aAAa,KAAK,kBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;oEACjC,WAAU;8EAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;8DAM3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAE5B,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EACV,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAO9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wDAC5D,IAAI,CAAC,KAAK,OAAO;wDACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wDACvE,IAAI,CAAC,eAAe,OAAO;wDAE3B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4DAEP,MAAM;4DACN,QAAO;4DACP,KAAI;4DACJ,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;sEAEvB,cAAA,6LAAC;gEAAc,MAAM;;;;;;2DARhB;;;;;oDAWX;;;;;;;;;;;;;;;;;;;;;;;0CAOR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,SAAQ;oEAAO,WAAU;8EAAkE;;;;;;8EAGlG,6LAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,6LAAC;;8EACC,6LAAC;oEAAM,SAAQ;oEAAQ,WAAU;8EAAkE;;;;;;8EAGnG,6LAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAkE;;;;;;sEAGrG,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAkE;;;;;;sEAGrG,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,MAAK;oDACL,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DACzB;;;;;;;;;;;;sDAKH,6LAAC;4CAAE,WAAU;;gDAA4D;gDAC/C;8DACxB,6LAAC;oDACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oDACpC,WAAU;8DAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC;KApOwB", "debugId": null}}, {"offset": {"line": 3791, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaHeart, FaGithub, FaLinkedin, FaTwitter, FaGlobe, FaArrowUp } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface FooterProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Footer({ personalInfo }: FooterProps) {\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <footer className=\"bg-gray-900 dark:bg-black text-white py-12\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"grid md:grid-cols-3 gap-8 mb-8\">\n          {/* About Section */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">{personalInfo.name}</h3>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              {personalInfo.title} passionate about creating innovative solutions \n              and contributing to meaningful projects.\n            </p>\n            <div className=\"flex gap-4\">\n              {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n                if (!url) return null;\n                const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n                if (!IconComponent) return null;\n\n                return (\n                  <motion.a\n                    key={platform}\n                    href={url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-300 hover:bg-indigo-600 hover:text-white transition-colors\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                  >\n                    <IconComponent size={18} />\n                  </motion.a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">Quick Links</h3>\n            <div className=\"space-y-2\">\n              {[\n                { name: 'About', href: '#about' },\n                { name: 'Projects', href: '#projects' },\n                { name: 'Experience', href: '#experience' },\n                { name: 'Contact', href: '#contact' },\n              ].map((link) => (\n                <motion.button\n                  key={link.name}\n                  onClick={() => {\n                    const element = document.getElementById(link.href.substring(1));\n                    element?.scrollIntoView({ behavior: 'smooth' });\n                  }}\n                  className=\"block text-gray-300 hover:text-indigo-400 transition-colors\"\n                  whileHover={{ x: 5 }}\n                >\n                  {link.name}\n                </motion.button>\n              ))}\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">Get In Touch</h3>\n            <div className=\"space-y-2 text-gray-300\">\n              <p>\n                <a\n                  href={`mailto:${personalInfo.email}`}\n                  className=\"hover:text-indigo-400 transition-colors\"\n                >\n                  {personalInfo.email}\n                </a>\n              </p>\n              {personalInfo.phone && (\n                <p>\n                  <a\n                    href={`tel:${personalInfo.phone}`}\n                    className=\"hover:text-indigo-400 transition-colors\"\n                  >\n                    {personalInfo.phone}\n                  </a>\n                </p>\n              )}\n              <p>{personalInfo.location}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"flex items-center gap-2 text-gray-300 mb-4 md:mb-0\">\n              <span>Made with</span>\n              <motion.div\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                <FaHeart className=\"text-red-500\" />\n              </motion.div>\n              <span>using Next.js, TypeScript & Tailwind CSS</span>\n            </div>\n\n            <motion.button\n              onClick={scrollToTop}\n              className=\"flex items-center gap-2 text-gray-300 hover:text-indigo-400 transition-colors\"\n              whileHover={{ y: -2 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span>Back to top</span>\n              <FaArrowUp />\n            </motion.button>\n          </div>\n\n          <div className=\"text-center text-gray-400 text-sm mt-4\">\n            <p>&copy; {new Date().getFullYear()} {personalInfo.name}. All rights reserved.</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,OAAO,EAAE,YAAY,EAAe;IAC1D,MAAM,cAAc;QAClB,QAAQ,iJAAA,CAAA,WAAQ;QAChB,UAAU,iJAAA,CAAA,aAAU;QACpB,SAAS,iJAAA,CAAA,YAAS;QAClB,SAAS,iJAAA,CAAA,UAAO;IAClB;IAEA,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA0B,aAAa,IAAI;;;;;;8CACzD,6LAAC;oCAAE,WAAU;;wCACV,aAAa,KAAK;wCAAC;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wCAC5D,IAAI,CAAC,KAAK,OAAO;wCACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wCACvE,IAAI,CAAC,eAAe,OAAO;wCAE3B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM;4CACN,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDAEvB,cAAA,6LAAC;gDAAc,MAAM;;;;;;2CARhB;;;;;oCAWX;;;;;;;;;;;;sCAKJ,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAS,MAAM;wCAAS;wCAChC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;wCACtC;4CAAE,MAAM;4CAAc,MAAM;wCAAc;wCAC1C;4CAAE,MAAM;4CAAW,MAAM;wCAAW;qCACrC,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,SAAS;gDACP,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC;gDAC5D,SAAS,eAAe;oDAAE,UAAU;gDAAS;4CAC/C;4CACA,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAE;sDAElB,KAAK,IAAI;2CARL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAetB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;gDACpC,WAAU;0DAET,aAAa,KAAK;;;;;;;;;;;wCAGtB,aAAa,KAAK,kBACjB,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;gDACjC,WAAU;0DAET,aAAa,KAAK;;;;;;;;;;;sDAIzB,6LAAC;sDAAG,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;sDAE5C,cAAA,6LAAC,iJAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC;sDAAK;;;;;;sDACN,6LAAC,iJAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;oCAAE;oCAAQ,IAAI,OAAO,WAAW;oCAAG;oCAAE,aAAa,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE;KAlIwB", "debugId": null}}]}