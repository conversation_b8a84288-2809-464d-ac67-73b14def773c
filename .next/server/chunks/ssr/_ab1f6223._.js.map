{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/About.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/About.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/About.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/About.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/About.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/About.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Skills.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Skills.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Skills.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Skills.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Skills.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Skills.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Projects.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Projects.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Projects.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Projects.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Education.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Education.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Education.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Education.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Education.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Education.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Experience.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Experience.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Experience.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Experience.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Experience.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Experience.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/AwardsActivities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AwardsActivities.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AwardsActivities.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/AwardsActivities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/AwardsActivities.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/AwardsActivities.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/data/portfolio.ts"], "sourcesContent": ["import { PersonalInfo, Project, Education, Experience, Award, Activity, Skill } from '@/types';\n\nexport const personalInfo: PersonalInfo = {\n  name: \"Your Name\",\n  title: \"Software Engineering Student\",\n  email: \"<EMAIL>\",\n  phone: \"+****************\",\n  location: \"Your City, Country\",\n  bio: \"Passionate software engineering student with a strong foundation in modern web technologies and a keen interest in building innovative solutions. Always eager to learn new technologies and contribute to meaningful projects.\",\n  profileImage: \"/images/profile.jpg\", // You'll place your photo here\n  socialLinks: {\n    github: \"https://github.com/yourusername\",\n    linkedin: \"https://linkedin.com/in/yourusername\",\n    twitter: \"https://twitter.com/yourusername\",\n    website: \"https://yourwebsite.com\"\n  }\n};\n\nexport const projects: Project[] = [\n  {\n    id: \"1\",\n    title: \"E-Commerce Platform\",\n    description: \"A full-stack e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.\",\n    technologies: [\"React\", \"Node.js\", \"MongoDB\", \"Express\", \"Stripe API\", \"JWT\"],\n    githubUrl: \"https://github.com/yourusername/ecommerce-platform\",\n    liveUrl: \"https://your-ecommerce-demo.com\",\n    imageUrl: \"/images/projects/ecommerce.jpg\",\n    featured: true\n  },\n  {\n    id: \"2\",\n    title: \"Task Management App\",\n    description: \"A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.\",\n    technologies: [\"Next.js\", \"TypeScript\", \"Prisma\", \"PostgreSQL\", \"Socket.io\"],\n    githubUrl: \"https://github.com/yourusername/task-manager\",\n    liveUrl: \"https://your-task-manager.com\",\n    imageUrl: \"/images/projects/task-manager.jpg\",\n    featured: true\n  },\n  {\n    id: \"3\",\n    title: \"Weather Dashboard\",\n    description: \"A responsive weather dashboard that displays current weather conditions and forecasts for multiple cities using external APIs.\",\n    technologies: [\"React\", \"TypeScript\", \"OpenWeather API\", \"Chart.js\", \"Tailwind CSS\"],\n    githubUrl: \"https://github.com/yourusername/weather-dashboard\",\n    liveUrl: \"https://your-weather-app.com\",\n    imageUrl: \"/images/projects/weather.jpg\",\n    featured: false\n  }\n];\n\nexport const education: Education[] = [\n  {\n    id: \"1\",\n    institution: \"Your University\",\n    degree: \"Bachelor of Science\",\n    field: \"Software Engineering\",\n    startDate: \"2021\",\n    endDate: \"2025\",\n    gpa: \"3.8/4.0\",\n    description: \"Relevant coursework: Data Structures & Algorithms, Software Design Patterns, Database Systems, Web Development, Mobile App Development, Computer Networks\"\n  },\n  {\n    id: \"2\",\n    institution: \"Your High School\",\n    degree: \"High School Diploma\",\n    field: \"Science Track\",\n    startDate: \"2017\",\n    endDate: \"2021\",\n    gpa: \"3.9/4.0\",\n    description: \"Graduated with honors. Active in computer science club and mathematics competitions.\"\n  }\n];\n\nexport const experiences: Experience[] = [\n  {\n    id: \"1\",\n    company: \"Tech Startup Inc.\",\n    position: \"Software Development Intern\",\n    startDate: \"June 2024\",\n    endDate: \"August 2024\",\n    description: \"Developed and maintained web applications using React and Node.js. Collaborated with senior developers on feature implementation and bug fixes. Participated in code reviews and agile development processes.\",\n    technologies: [\"React\", \"Node.js\", \"PostgreSQL\", \"Git\", \"Docker\"],\n    type: \"internship\"\n  },\n  {\n    id: \"2\",\n    company: \"University IT Department\",\n    position: \"Student Developer\",\n    startDate: \"September 2023\",\n    endDate: \"Present\",\n    description: \"Part-time position developing internal tools and maintaining university websites. Responsible for frontend development and user experience improvements.\",\n    technologies: [\"Vue.js\", \"PHP\", \"MySQL\", \"WordPress\"],\n    type: \"part-time\"\n  }\n];\n\nexport const awards: Award[] = [\n  {\n    id: \"1\",\n    title: \"Dean's List\",\n    organization: \"Your University\",\n    date: \"Fall 2023\",\n    description: \"Achieved Dean's List recognition for academic excellence with a GPA of 3.8 or higher.\"\n  },\n  {\n    id: \"2\",\n    title: \"Best Innovation Award\",\n    organization: \"University Hackathon 2024\",\n    date: \"March 2024\",\n    description: \"Won first place for developing an AI-powered study assistant application during the 48-hour hackathon.\"\n  },\n  {\n    id: \"3\",\n    title: \"Programming Competition Winner\",\n    organization: \"Regional Coding Contest\",\n    date: \"November 2023\",\n    description: \"Placed 1st in regional programming competition, solving complex algorithmic problems.\"\n  }\n];\n\nexport const activities: Activity[] = [\n  {\n    id: \"1\",\n    title: \"Computer Science Club\",\n    organization: \"Your University\",\n    role: \"Vice President\",\n    startDate: \"September 2023\",\n    endDate: \"Present\",\n    description: \"Organize coding workshops, tech talks, and networking events. Mentor junior students in programming and career development.\"\n  },\n  {\n    id: \"2\",\n    title: \"Open Source Contributor\",\n    organization: \"Various Projects\",\n    role: \"Contributor\",\n    startDate: \"January 2023\",\n    endDate: \"Present\",\n    description: \"Active contributor to open source projects on GitHub. Focus on web development tools and educational resources.\"\n  },\n  {\n    id: \"3\",\n    title: \"Volunteer Coding Instructor\",\n    organization: \"Local Community Center\",\n    role: \"Instructor\",\n    startDate: \"June 2023\",\n    endDate: \"Present\",\n    description: \"Teach basic programming concepts to high school students during summer programs.\"\n  }\n];\n\nexport const skills: Skill[] = [\n  // Frontend\n  { name: \"React\", category: \"frontend\", proficiency: \"advanced\" },\n  { name: \"Next.js\", category: \"frontend\", proficiency: \"intermediate\" },\n  { name: \"TypeScript\", category: \"frontend\", proficiency: \"intermediate\" },\n  { name: \"JavaScript\", category: \"frontend\", proficiency: \"advanced\" },\n  { name: \"HTML/CSS\", category: \"frontend\", proficiency: \"advanced\" },\n  { name: \"Tailwind CSS\", category: \"frontend\", proficiency: \"intermediate\" },\n  { name: \"Vue.js\", category: \"frontend\", proficiency: \"intermediate\" },\n  \n  // Backend\n  { name: \"Node.js\", category: \"backend\", proficiency: \"intermediate\" },\n  { name: \"Express.js\", category: \"backend\", proficiency: \"intermediate\" },\n  { name: \"Python\", category: \"backend\", proficiency: \"intermediate\" },\n  { name: \"Java\", category: \"backend\", proficiency: \"intermediate\" },\n  { name: \"PHP\", category: \"backend\", proficiency: \"beginner\" },\n  \n  // Database\n  { name: \"MongoDB\", category: \"database\", proficiency: \"intermediate\" },\n  { name: \"PostgreSQL\", category: \"database\", proficiency: \"intermediate\" },\n  { name: \"MySQL\", category: \"database\", proficiency: \"intermediate\" },\n  \n  // Tools\n  { name: \"Git\", category: \"tools\", proficiency: \"advanced\" },\n  { name: \"Docker\", category: \"tools\", proficiency: \"beginner\" },\n  { name: \"VS Code\", category: \"tools\", proficiency: \"advanced\" },\n  { name: \"Figma\", category: \"tools\", proficiency: \"intermediate\" },\n  \n  // Languages\n  { name: \"English\", category: \"languages\", proficiency: \"expert\" },\n  { name: \"French\", category: \"languages\", proficiency: \"intermediate\" },\n  { name: \"Spanish\", category: \"languages\", proficiency: \"beginner\" }\n];\n"], "names": [], "mappings": ";;;;;;;;;AAEO,MAAM,eAA6B;IACxC,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,UAAU;IACV,KAAK;IACL,cAAc;IACd,aAAa;QACX,QAAQ;QACR,UAAU;QACV,SAAS;QACT,SAAS;IACX;AACF;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAS;YAAW;YAAW;YAAW;YAAc;SAAM;QAC7E,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAW;YAAc;YAAU;YAAc;SAAY;QAC5E,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;YAAC;YAAS;YAAc;YAAmB;YAAY;SAAe;QACpF,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAEM,MAAM,YAAyB;IACpC;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,WAAW;QACX,SAAS;QACT,KAAK;QACL,aAAa;IACf;IACA;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,WAAW;QACX,SAAS;QACT,KAAK;QACL,aAAa;IACf;CACD;AAEM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,WAAW;QACX,SAAS;QACT,aAAa;QACb,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;SAAS;QACjE,MAAM;IACR;IACA;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,WAAW;QACX,SAAS;QACT,aAAa;QACb,cAAc;YAAC;YAAU;YAAO;YAAS;SAAY;QACrD,MAAM;IACR;CACD;AAEM,MAAM,SAAkB;IAC7B;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,aAAa;IACf;CACD;AAEM,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,WAAW;QACX,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,WAAW;QACX,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,cAAc;QACd,MAAM;QACN,WAAW;QACX,SAAS;QACT,aAAa;IACf;CACD;AAEM,MAAM,SAAkB;IAC7B,WAAW;IACX;QAAE,MAAM;QAAS,UAAU;QAAY,aAAa;IAAW;IAC/D;QAAE,MAAM;QAAW,UAAU;QAAY,aAAa;IAAe;IACrE;QAAE,MAAM;QAAc,UAAU;QAAY,aAAa;IAAe;IACxE;QAAE,MAAM;QAAc,UAAU;QAAY,aAAa;IAAW;IACpE;QAAE,MAAM;QAAY,UAAU;QAAY,aAAa;IAAW;IAClE;QAAE,MAAM;QAAgB,UAAU;QAAY,aAAa;IAAe;IAC1E;QAAE,MAAM;QAAU,UAAU;QAAY,aAAa;IAAe;IAEpE,UAAU;IACV;QAAE,MAAM;QAAW,UAAU;QAAW,aAAa;IAAe;IACpE;QAAE,MAAM;QAAc,UAAU;QAAW,aAAa;IAAe;IACvE;QAAE,MAAM;QAAU,UAAU;QAAW,aAAa;IAAe;IACnE;QAAE,MAAM;QAAQ,UAAU;QAAW,aAAa;IAAe;IACjE;QAAE,MAAM;QAAO,UAAU;QAAW,aAAa;IAAW;IAE5D,WAAW;IACX;QAAE,MAAM;QAAW,UAAU;QAAY,aAAa;IAAe;IACrE;QAAE,MAAM;QAAc,UAAU;QAAY,aAAa;IAAe;IACxE;QAAE,MAAM;QAAS,UAAU;QAAY,aAAa;IAAe;IAEnE,QAAQ;IACR;QAAE,MAAM;QAAO,UAAU;QAAS,aAAa;IAAW;IAC1D;QAAE,MAAM;QAAU,UAAU;QAAS,aAAa;IAAW;IAC7D;QAAE,MAAM;QAAW,UAAU;QAAS,aAAa;IAAW;IAC9D;QAAE,MAAM;QAAS,UAAU;QAAS,aAAa;IAAe;IAEhE,YAAY;IACZ;QAAE,MAAM;QAAW,UAAU;QAAa,aAAa;IAAS;IAChE;QAAE,MAAM;QAAU,UAAU;QAAa,aAAa;IAAe;IACrE;QAAE,MAAM;QAAW,UAAU;QAAa,aAAa;IAAW;CACnE", "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/app/page.tsx"], "sourcesContent": ["import Navigation from '@/components/Navigation';\nimport Hero from '@/components/Hero';\nimport About from '@/components/About';\nimport Skills from '@/components/Skills';\nimport Projects from '@/components/Projects';\nimport Education from '@/components/Education';\nimport Experience from '@/components/Experience';\nimport AwardsActivities from '@/components/AwardsActivities';\nimport Contact from '@/components/Contact';\nimport Footer from '@/components/Footer';\nimport { personalInfo, projects, education, experiences, awards, activities, skills } from '@/data/portfolio';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Navigation />\n\n      <main>\n        <section id=\"home\">\n          <Hero personalInfo={personalInfo} />\n        </section>\n\n        <About personalInfo={personalInfo} />\n        <Skills skills={skills} />\n        <Projects projects={projects} />\n        <Education education={education} />\n        <Experience experiences={experiences} />\n        <AwardsActivities awards={awards} activities={activities} />\n        <Contact personalInfo={personalInfo} />\n      </main>\n\n      <Footer personalInfo={personalInfo} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;;kCACC,8OAAC;wBAAQ,IAAG;kCACV,cAAA,8OAAC,0HAAA,CAAA,UAAI;4BAAC,cAAc,wHAAA,CAAA,eAAY;;;;;;;;;;;kCAGlC,8OAAC,2HAAA,CAAA,UAAK;wBAAC,cAAc,wHAAA,CAAA,eAAY;;;;;;kCACjC,8OAAC,4HAAA,CAAA,UAAM;wBAAC,QAAQ,wHAAA,CAAA,SAAM;;;;;;kCACtB,8OAAC,8HAAA,CAAA,UAAQ;wBAAC,UAAU,wHAAA,CAAA,WAAQ;;;;;;kCAC5B,8OAAC,+HAAA,CAAA,UAAS;wBAAC,WAAW,wHAAA,CAAA,YAAS;;;;;;kCAC/B,8OAAC,gIAAA,CAAA,UAAU;wBAAC,aAAa,wHAAA,CAAA,cAAW;;;;;;kCACpC,8OAAC,sIAAA,CAAA,UAAgB;wBAAC,QAAQ,wHAAA,CAAA,SAAM;wBAAE,YAAY,wHAAA,CAAA,aAAU;;;;;;kCACxD,8OAAC,6HAAA,CAAA,UAAO;wBAAC,cAAc,wHAAA,CAAA,eAAY;;;;;;;;;;;;0BAGrC,8OAAC,4HAAA,CAAA,UAAM;gBAAC,cAAc,wHAAA,CAAA,eAAY;;;;;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}