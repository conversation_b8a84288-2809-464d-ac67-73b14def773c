{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaBars, FaTimes, FaMoon, FaSun } from 'react-icons/fa';\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('');\n  const [isDark, setIsDark] = useState(false);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Education', href: '#education' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Awards', href: '#awards-activities' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    } else {\n      setIsDark(false);\n      document.documentElement.classList.remove('dark');\n    }\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = navItems.map(item => item.href.substring(1));\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const offsetTop = element.offsetTop;\n          const offsetHeight = element.offsetHeight;\n\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Call once to set initial active section\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.getElementById(href.substring(1));\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsOpen(false);\n  };\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className=\"fixed top-0 left-0 right-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md border-b border-gray-200 dark:border-gray-700\"\n    >\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-xl font-bold text-gray-900 dark:text-white\"\n            whileHover={{ scale: 1.05 }}\n          >\n            Portfolio\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.button\n                key={item.name}\n                onClick={() => scrollToSection(item.href)}\n                className={`text-sm font-medium transition-colors ${\n                  activeSection === item.href.substring(1)\n                    ? 'text-indigo-600 dark:text-indigo-400'\n                    : 'text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400'\n                }`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {item.name}\n              </motion.button>\n            ))}\n            \n            {/* Theme Toggle */}\n            <motion.button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isDark ? <FaSun size={18} /> : <FaMoon size={18} />}\n            </motion.button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center gap-4\">\n            <motion.button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isDark ? <FaSun size={18} /> : <FaMoon size={18} />}\n            </motion.button>\n            \n            <motion.button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {isOpen ? <FaTimes size={20} /> : <FaBars size={20} />}\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden border-t border-gray-200 dark:border-gray-700\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item) => (\n                  <motion.button\n                    key={item.name}\n                    onClick={() => scrollToSection(item.href)}\n                    className={`block w-full text-left px-4 py-2 text-sm font-medium transition-colors ${\n                      activeSection === item.href.substring(1)\n                        ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30'\n                        : 'text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-50 dark:hover:bg-gray-800'\n                    }`}\n                    whileHover={{ x: 5 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {item.name}\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAU,MAAM;QAAqB;QAC7C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAE7E,IAAI,eAAe,UAAW,CAAC,cAAc,aAAc;YACzD,UAAU;YACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,UAAU;YACV,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,SAAS,CAAC;YAC1D,MAAM,iBAAiB,OAAO,OAAO,GAAG;YAExC,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,YAAY,QAAQ,SAAS;oBACnC,MAAM,eAAe,QAAQ,YAAY;oBAEzC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;wBAC5E,iBAAiB;wBACjB;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,gBAAgB,0CAA0C;QAE1D,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;QACvD,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,UAAU;IACZ;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;sCAC3B;;;;;;sCAKD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAW,CAAC,sCAAsC,EAChD,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,yCACA,qFACJ;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAEvB,KAAK,IAAI;uCAVL,KAAK,IAAI;;;;;8CAelB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,8OAAC,8IAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,8OAAC,8IAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,8OAAC,8IAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,8OAAC,8IAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAGhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,UAAU,CAAC;oCAC1B,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;8CAEtB,uBAAS,8OAAC,8IAAA,CAAA,UAAO;wCAAC,MAAM;;;;;6DAAS,8OAAC,8IAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8BAMtD,8OAAC,yLAAA,CAAA,kBAAe;8BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAW,CAAC,uEAAuE,EACjF,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,4EACA,6HACJ;oCACF,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,IAAI;mCAVL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBlC", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaGlobe, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface HeroProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Hero({ personalInfo }: HeroProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 px-4\">\n      <motion.div\n        className=\"max-w-4xl mx-auto text-center\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <motion.div\n          variants={itemVariants}\n          className=\"mb-8\"\n        >\n          {personalInfo.profileImage && (\n            <div className=\"w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-white shadow-lg\">\n              <img\n                src={personalInfo.profileImage}\n                alt={personalInfo.name}\n                className=\"w-full h-full object-cover\"\n                onError={(e) => {\n                  // Fallback to a placeholder if image fails to load\n                  e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(personalInfo.name)}&size=128&background=6366f1&color=ffffff`;\n                }}\n              />\n            </div>\n          )}\n        </motion.div>\n\n        <motion.h1\n          variants={itemVariants}\n          className=\"text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4\"\n        >\n          {personalInfo.name}\n        </motion.h1>\n\n        <motion.h2\n          variants={itemVariants}\n          className=\"text-xl md:text-2xl text-indigo-600 dark:text-indigo-400 mb-6 font-medium\"\n        >\n          {personalInfo.title}\n        </motion.h2>\n\n        <motion.p\n          variants={itemVariants}\n          className=\"text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed\"\n        >\n          {personalInfo.bio}\n        </motion.p>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"flex flex-wrap justify-center gap-4 mb-8 text-sm text-gray-600 dark:text-gray-300\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <FaEnvelope className=\"text-indigo-600 dark:text-indigo-400\" />\n            <a href={`mailto:${personalInfo.email}`} className=\"hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\">\n              {personalInfo.email}\n            </a>\n          </div>\n          {personalInfo.phone && (\n            <div className=\"flex items-center gap-2\">\n              <FaPhone className=\"text-indigo-600 dark:text-indigo-400\" />\n              <a href={`tel:${personalInfo.phone}`} className=\"hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\">\n                {personalInfo.phone}\n              </a>\n            </div>\n          )}\n          <div className=\"flex items-center gap-2\">\n            <FaMapMarkerAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n            <span>{personalInfo.location}</span>\n          </div>\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"flex justify-center gap-6\"\n        >\n          {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n            if (!url) return null;\n            const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n            if (!IconComponent) return null;\n\n            return (\n              <motion.a\n                key={platform}\n                href={url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-2xl text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                whileHover={{ scale: 1.2 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <IconComponent />\n              </motion.a>\n            );\n          })}\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          className=\"mt-12\"\n        >\n          <motion.button\n            onClick={() => {\n              const aboutSection = document.getElementById('about');\n              aboutSection?.scrollIntoView({ behavior: 'smooth' });\n            }}\n            className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-full font-medium transition-colors shadow-lg hover:shadow-xl\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Learn More About Me\n          </motion.button>\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,KAAK,EAAE,YAAY,EAAa;IACtD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ,8IAAA,CAAA,WAAQ;QAChB,UAAU,8IAAA,CAAA,aAAU;QACpB,SAAS,8IAAA,CAAA,YAAS;QAClB,SAAS,8IAAA,CAAA,UAAO;IAClB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,UAAU;YACV,SAAQ;YACR,SAAQ;;8BAER,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,aAAa,YAAY,kBACxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK,aAAa,YAAY;4BAC9B,KAAK,aAAa,IAAI;4BACtB,WAAU;4BACV,SAAS,CAAC;gCACR,mDAAmD;gCACnD,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,iCAAiC,EAAE,mBAAmB,aAAa,IAAI,EAAE,wCAAwC,CAAC;4BAC3I;;;;;;;;;;;;;;;;8BAMR,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,UAAU;oBACV,WAAU;8BAET,aAAa,IAAI;;;;;;8BAGpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,UAAU;oBACV,WAAU;8BAET,aAAa,KAAK;;;;;;8BAGrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,UAAU;oBACV,WAAU;8BAET,aAAa,GAAG;;;;;;8BAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAE,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oCAAE,WAAU;8CAChD,aAAa,KAAK;;;;;;;;;;;;wBAGtB,aAAa,KAAK,kBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;oCAAE,WAAU;8CAC7C,aAAa,KAAK;;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;8CAC1B,8OAAC;8CAAM,aAAa,QAAQ;;;;;;;;;;;;;;;;;;8BAIhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAET,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wBAC5D,IAAI,CAAC,KAAK,OAAO;wBACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wBACvE,IAAI,CAAC,eAAe,OAAO;wBAE3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BAEP,MAAM;4BACN,QAAO;4BACP,KAAI;4BACJ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEvB,cAAA,8OAAC;;;;;2BARI;;;;;oBAWX;;;;;;8BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BACP,MAAM,eAAe,SAAS,cAAc,CAAC;4BAC7C,cAAc,eAAe;gCAAE,UAAU;4BAAS;wBACpD;wBACA,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { PersonalInfo } from '@/types';\n\ninterface AboutProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function About({ personalInfo }: AboutProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              About Me\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto\"></div>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 gap-12 items-center\">\n            <motion.div variants={itemVariants}>\n              <div className=\"space-y-6\">\n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  {personalInfo.bio}\n                </p>\n                \n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  I'm passionate about creating efficient, scalable, and user-friendly applications. \n                  My journey in software engineering has equipped me with a strong foundation in \n                  various programming languages and frameworks, and I'm always eager to learn \n                  new technologies and tackle challenging problems.\n                </p>\n\n                <p className=\"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\">\n                  When I'm not coding, you can find me contributing to open source projects, \n                  participating in hackathons, or mentoring fellow students. I believe in the \n                  power of technology to make a positive impact on the world.\n                </p>\n\n                <div className=\"flex flex-wrap gap-4 mt-8\">\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Problem Solver</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Team Player</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Quick Learner</span>\n                  </div>\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/30 px-4 py-2 rounded-lg\">\n                    <span className=\"text-indigo-600 dark:text-indigo-400 font-medium\">Creative Thinker</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 p-8 rounded-2xl\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Quick Facts\n                </h3>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Location</span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{personalInfo.location}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Current Role</span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{personalInfo.title}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-600 dark:text-gray-300\">Email</span>\n                    <a \n                      href={`mailto:${personalInfo.email}`}\n                      className=\"font-medium text-indigo-600 dark:text-indigo-400 hover:underline\"\n                    >\n                      {personalInfo.email}\n                    </a>\n                  </div>\n                  \n                  {personalInfo.phone && (\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-gray-600 dark:text-gray-300\">Phone</span>\n                      <a \n                        href={`tel:${personalInfo.phone}`}\n                        className=\"font-medium text-indigo-600 dark:text-indigo-400 hover:underline\"\n                      >\n                        {personalInfo.phone}\n                      </a>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mt-8\">\n                  <a\n                    href=\"/resume.pdf\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center justify-center w-full bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\"\n                  >\n                    Download Resume\n                  </a>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,MAAM,EAAE,YAAY,EAAc;IACxD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,aAAa,GAAG;;;;;;sDAGnB,8OAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAOxE,8OAAC;4CAAE,WAAU;sDAA2D;;;;;;sDAMxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;8DAErE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DAAK,WAAU;sEAA6C,aAAa,QAAQ;;;;;;;;;;;;8DAGpF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DAAK,WAAU;sEAA6C,aAAa,KAAK;;;;;;;;;;;;8DAGjF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;4DACpC,WAAU;sEAET,aAAa,KAAK;;;;;;;;;;;;gDAItB,aAAa,KAAK,kBACjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;4DACjC,WAAU;sEAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;sDAM3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Skill } from '@/types';\n\ninterface SkillsProps {\n  skills: Skill[];\n}\n\nexport default function Skills({ skills }: SkillsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const skillCategories = {\n    frontend: 'Frontend',\n    backend: 'Backend',\n    database: 'Database',\n    tools: 'Tools & Technologies',\n    languages: 'Languages',\n    other: 'Other'\n  };\n\n  const proficiencyColors = {\n    beginner: 'bg-yellow-200 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n    intermediate: 'bg-blue-200 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n    advanced: 'bg-green-200 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n    expert: 'bg-purple-200 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'\n  };\n\n  const groupedSkills = skills.reduce((acc, skill) => {\n    if (!acc[skill.category]) {\n      acc[skill.category] = [];\n    }\n    acc[skill.category].push(skill);\n    return acc;\n  }, {} as Record<string, Skill[]>);\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Skills & Technologies\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Here are the technologies and tools I work with to bring ideas to life.\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {Object.entries(groupedSkills).map(([category, categorySkills]) => (\n              <motion.div\n                key={category}\n                variants={itemVariants}\n                className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n              >\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n                  {skillCategories[category as keyof typeof skillCategories]}\n                </h3>\n                \n                <div className=\"space-y-3\">\n                  {categorySkills.map((skill, index) => (\n                    <motion.div\n                      key={skill.name}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                      className=\"flex items-center justify-between\"\n                    >\n                      <span className=\"text-gray-700 dark:text-gray-300 font-medium\">\n                        {skill.name}\n                      </span>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${proficiencyColors[skill.proficiency]}`}>\n                        {skill.proficiency}\n                      </span>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                Proficiency Legend\n              </h3>\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                {Object.entries(proficiencyColors).map(([level, colorClass]) => (\n                  <div key={level} className=\"flex items-center gap-2\">\n                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${colorClass}`}>\n                      {level}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,OAAO,EAAE,MAAM,EAAe;IACpD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,WAAW;QACX,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,UAAU;QACV,cAAc;QACd,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,KAAK;QACxC,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,EAAE;YACxB,GAAG,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE;QAC1B;QACA,GAAG,CAAC,MAAM,QAAQ,CAAC,CAAC,IAAI,CAAC;QACzB,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,eAAe,iBAC5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDACX,eAAe,CAAC,SAAyC;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEACb,MAAM,IAAI;;;;;;kEAEb,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,CAAC,MAAM,WAAW,CAAC,EAAE;kEAClG,MAAM,WAAW;;;;;;;+CAVf,MAAM,IAAI;;;;;;;;;;;+BAXhB;;;;;;;;;;kCA8BX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,OAAO,WAAW,iBACzD,8OAAC;4CAAgB,WAAU;sDACzB,cAAA,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY;0DACxE;;;;;;2CAFK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa5B", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaStar } from 'react-icons/fa';\nimport { Project } from '@/types';\n\ninterface ProjectsProps {\n  projects: Project[];\n}\n\nexport default function Projects({ projects }: ProjectsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const featuredProjects = projects.filter(project => project.featured);\n  const otherProjects = projects.filter(project => !project.featured);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Featured Projects\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Here are some of the projects I've worked on that showcase my skills and passion for development.\n            </p>\n          </motion.div>\n\n          {/* Featured Projects */}\n          <div className=\"space-y-12 mb-16\">\n            {featuredProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                variants={itemVariants}\n                className={`grid md:grid-cols-2 gap-8 items-center ${\n                  index % 2 === 1 ? 'md:grid-flow-col-dense' : ''\n                }`}\n              >\n                <div className={`${index % 2 === 1 ? 'md:col-start-2' : ''}`}>\n                  <div className=\"relative group\">\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200\"></div>\n                    <div className=\"relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden aspect-video\">\n                      {project.imageUrl ? (\n                        <img\n                          src={project.imageUrl}\n                          alt={project.title}\n                          className=\"w-full h-full object-cover\"\n                          onError={(e) => {\n                            e.currentTarget.src = `https://via.placeholder.com/600x400/6366f1/ffffff?text=${encodeURIComponent(project.title)}`;\n                          }}\n                        />\n                      ) : (\n                        <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600\">\n                          <span className=\"text-white text-2xl font-bold\">{project.title}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                <div className={`${index % 2 === 1 ? 'md:col-start-1 md:row-start-1' : ''}`}>\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <FaStar className=\"text-yellow-500\" />\n                    <span className=\"text-sm font-medium text-indigo-600 dark:text-indigo-400\">\n                      Featured Project\n                    </span>\n                  </div>\n                  \n                  <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                    {project.title}\n                  </h3>\n                  \n                  <p className=\"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\">\n                    {project.description}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-2 mb-6\">\n                    {project.technologies.map((tech) => (\n                      <span\n                        key={tech}\n                        className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-sm font-medium\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                  \n                  <div className=\"flex gap-4\">\n                    {project.githubUrl && (\n                      <motion.a\n                        href={project.githubUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <FaGithub />\n                        <span>Code</span>\n                      </motion.a>\n                    )}\n                    {project.liveUrl && (\n                      <motion.a\n                        href={project.liveUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <FaExternalLinkAlt />\n                        <span>Live Demo</span>\n                      </motion.a>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Other Projects */}\n          {otherProjects.length > 0 && (\n            <>\n              <motion.div variants={itemVariants} className=\"text-center mb-12\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                  Other Projects\n                </h3>\n              </motion.div>\n\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {otherProjects.map((project) => (\n                  <motion.div\n                    key={project.id}\n                    variants={itemVariants}\n                    className=\"bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow\"\n                    whileHover={{ y: -5 }}\n                  >\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                      {project.title}\n                    </h4>\n                    \n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed\">\n                      {project.description}\n                    </p>\n                    \n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {project.technologies.slice(0, 3).map((tech) => (\n                        <span\n                          key={tech}\n                          className=\"px-2 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded text-xs font-medium\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                      {project.technologies.length > 3 && (\n                        <span className=\"px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs\">\n                          +{project.technologies.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex gap-4\">\n                      {project.githubUrl && (\n                        <a\n                          href={project.githubUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          <FaGithub size={18} />\n                        </a>\n                      )}\n                      {project.liveUrl && (\n                        <a\n                          href={project.liveUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          <FaExternalLinkAlt size={16} />\n                        </a>\n                      )}\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,SAAS,EAAE,QAAQ,EAAiB;IAC1D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpE,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ;IAElE,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAM5E,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAW,CAAC,uCAAuC,EACjD,QAAQ,MAAM,IAAI,2BAA2B,IAC7C;;kDAEF,8OAAC;wCAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,mBAAmB,IAAI;kDAC1D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ,iBACf,8OAAC;wDACC,KAAK,QAAQ,QAAQ;wDACrB,KAAK,QAAQ,KAAK;wDAClB,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,uDAAuD,EAAE,mBAAmB,QAAQ,KAAK,GAAG;wDACrH;;;;;6EAGF,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAiC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxE,8OAAC;wCAAI,WAAW,GAAG,QAAQ,MAAM,IAAI,kCAAkC,IAAI;;0DACzE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAK7E,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;0DAQX,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,SAAS,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAM,QAAQ,SAAS;wDACvB,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC,8IAAA,CAAA,WAAQ;;;;;0EACT,8OAAC;0EAAK;;;;;;;;;;;;oDAGT,QAAQ,OAAO,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,MAAM,QAAQ,OAAO;wDACrB,QAAO;wDACP,KAAI;wDACJ,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC,8IAAA,CAAA,oBAAiB;;;;;0EAClB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;+BA/ET,QAAQ,EAAE;;;;;;;;;;oBAyFpB,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;0CAC5C,cAAA,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;;;;;;0CAKxE,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;wCACV,YAAY;4CAAE,GAAG,CAAC;wCAAE;;0DAEpB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;oDAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;wDAAK,WAAU;;4DAA0F;4DACtG,QAAQ,YAAY,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;0DAKxC,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,SAAS,kBAChB,8OAAC;wDACC,MAAM,QAAQ,SAAS;wDACvB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAEV,cAAA,8OAAC,8IAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;;;;;;oDAGnB,QAAQ,OAAO,kBACd,8OAAC;wDACC,MAAM,QAAQ,OAAO;wDACrB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAEV,cAAA,8OAAC,8IAAA,CAAA,oBAAiB;4DAAC,MAAM;;;;;;;;;;;;;;;;;;uCA/C1B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DnC", "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Education.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaGraduationCap, FaCalendarAlt, FaStar } from 'react-icons/fa';\nimport { Education } from '@/types';\n\ninterface EducationProps {\n  education: Education[];\n}\n\nexport default function Education({ education }: EducationProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"education\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Education\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              My academic journey and the foundation of my technical knowledge.\n            </p>\n          </motion.div>\n\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-indigo-200 dark:bg-indigo-800 hidden md:block\"></div>\n\n            <div className=\"space-y-12\">\n              {education.map((edu, index) => (\n                <motion.div\n                  key={edu.id}\n                  variants={itemVariants}\n                  className=\"relative\"\n                >\n                  {/* Timeline dot */}\n                  <div className=\"absolute left-6 w-4 h-4 bg-indigo-600 rounded-full border-4 border-white dark:border-gray-800 hidden md:block\"></div>\n\n                  <div className=\"md:ml-20\">\n                    <motion.div\n                      className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\n                      whileHover={{ y: -5 }}\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-start md:justify-between mb-4\">\n                        <div className=\"flex items-center gap-3 mb-2 md:mb-0\">\n                          <FaGraduationCap className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                            {edu.degree} in {edu.field}\n                          </h3>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                          <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                          <span>{edu.startDate} - {edu.endDate}</span>\n                        </div>\n                      </div>\n\n                      <h4 className=\"text-lg font-semibold text-indigo-600 dark:text-indigo-400 mb-4\">\n                        {edu.institution}\n                      </h4>\n\n                      {edu.gpa && (\n                        <div className=\"flex items-center gap-2 mb-4\">\n                          <FaStar className=\"text-yellow-500\" />\n                          <span className=\"text-gray-700 dark:text-gray-300\">\n                            GPA: <span className=\"font-semibold\">{edu.gpa}</span>\n                          </span>\n                        </div>\n                      )}\n\n                      {edu.description && (\n                        <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed\">\n                          {edu.description}\n                        </p>\n                      )}\n\n                      {/* Progress indicator for current education */}\n                      {edu.endDate.toLowerCase().includes('present') || \n                       edu.endDate.toLowerCase().includes('current') || \n                       parseInt(edu.endDate) > new Date().getFullYear() && (\n                        <div className=\"mt-4\">\n                          <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                            <span>Progress</span>\n                            <span>In Progress</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <motion.div\n                              className=\"bg-indigo-600 h-2 rounded-full\"\n                              initial={{ width: 0 }}\n                              whileInView={{ width: '75%' }}\n                              transition={{ duration: 1, delay: 0.5 }}\n                            ></motion.div>\n                          </div>\n                        </div>\n                      )}\n                    </motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Additional achievements or certifications section */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                Academic Achievements\n              </h3>\n              \n              <div className=\"grid md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaGraduationCap className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Dean's List</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Multiple semesters of academic excellence\n                  </p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaStar className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Honor Society</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Member of Computer Science Honor Society\n                  </p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaCalendarAlt className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">Expected Graduation</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    On track for timely completion\n                  </p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,UAAU,EAAE,SAAS,EAAkB;IAC7D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAY,WAAU;kBAChC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG,CAAC;oDAAE;;sEAEpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,kBAAe;4EAAC,WAAU;;;;;;sFAC3B,8OAAC;4EAAG,WAAU;;gFACX,IAAI,MAAM;gFAAC;gFAAK,IAAI,KAAK;;;;;;;;;;;;;8EAI9B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;sFACzB,8OAAC;;gFAAM,IAAI,SAAS;gFAAC;gFAAI,IAAI,OAAO;;;;;;;;;;;;;;;;;;;sEAIxC,8OAAC;4DAAG,WAAU;sEACX,IAAI,WAAW;;;;;;wDAGjB,IAAI,GAAG,kBACN,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;;wEAAmC;sFAC5C,8OAAC;4EAAK,WAAU;sFAAiB,IAAI,GAAG;;;;;;;;;;;;;;;;;;wDAKlD,IAAI,WAAW,kBACd,8OAAC;4DAAE,WAAU;sEACV,IAAI,WAAW;;;;;;wDAKnB,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,SAAS,IAAI,OAAO,IAAI,IAAI,OAAO,WAAW,oBAC7C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,SAAS;4EAAE,OAAO;wEAAE;wEACpB,aAAa;4EAAE,OAAO;wEAAM;wEAC5B,YAAY;4EAAE,UAAU;4EAAG,OAAO;wEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3D7C,IAAI,EAAE;;;;;;;;;;;;;;;;kCAwEnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAItE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAK1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAK1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E", "debugId": null}}, {"offset": {"line": 2131, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Experience.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaBriefcase, FaCalendarAlt, FaMapMarkerAlt } from 'react-icons/fa';\nimport { Experience } from '@/types';\n\ninterface ExperienceProps {\n  experiences: Experience[];\n}\n\nexport default function Experience({ experiences }: ExperienceProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'internship':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'full-time':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';\n      case 'part-time':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'contract':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getTypeLabel = (type: string) => {\n    return type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ');\n  };\n\n  return (\n    <section id=\"experience\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Work Experience\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              My professional journey and the hands-on experience I've gained in the industry.\n            </p>\n          </motion.div>\n\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-indigo-200 dark:bg-indigo-800 hidden md:block\"></div>\n\n            <div className=\"space-y-12\">\n              {experiences.map((exp, index) => (\n                <motion.div\n                  key={exp.id}\n                  variants={itemVariants}\n                  className=\"relative\"\n                >\n                  {/* Timeline dot */}\n                  <div className=\"absolute left-6 w-4 h-4 bg-indigo-600 rounded-full border-4 border-white dark:border-gray-900 hidden md:block\"></div>\n\n                  <div className=\"md:ml-20\">\n                    <motion.div\n                      className=\"bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow\"\n                      whileHover={{ y: -5 }}\n                    >\n                      <div className=\"flex flex-col md:flex-row md:items-start md:justify-between mb-4\">\n                        <div className=\"flex items-center gap-3 mb-2 md:mb-0\">\n                          <FaBriefcase className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                          <div>\n                            <h3 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                              {exp.position}\n                            </h3>\n                            <h4 className=\"text-lg font-semibold text-indigo-600 dark:text-indigo-400\">\n                              {exp.company}\n                            </h4>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex flex-col gap-2\">\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{exp.startDate} - {exp.endDate}</span>\n                          </div>\n                          \n                          <span className={`px-3 py-1 rounded-full text-xs font-medium self-start ${getTypeColor(exp.type)}`}>\n                            {getTypeLabel(exp.type)}\n                          </span>\n                        </div>\n                      </div>\n\n                      <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed mb-6\">\n                        {exp.description}\n                      </p>\n\n                      <div className=\"space-y-4\">\n                        <div>\n                          <h5 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-2\">\n                            Technologies Used:\n                          </h5>\n                          <div className=\"flex flex-wrap gap-2\">\n                            {exp.technologies.map((tech) => (\n                              <span\n                                key={tech}\n                                className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-sm font-medium\"\n                              >\n                                {tech}\n                              </span>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Current position indicator */}\n                        {(exp.endDate.toLowerCase().includes('present') || \n                          exp.endDate.toLowerCase().includes('current')) && (\n                          <div className=\"flex items-center gap-2 text-sm\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                            <span className=\"text-green-600 dark:text-green-400 font-medium\">\n                              Currently working here\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Call to action for more opportunities */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 text-center\"\n          >\n            <div className=\"bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                Looking for New Opportunities\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto\">\n                I'm always interested in new challenges and opportunities to grow. \n                If you have an exciting project or position, I'd love to hear from you!\n              </p>\n              <motion.a\n                href=\"#contact\"\n                className=\"inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg font-medium transition-colors\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={(e) => {\n                  e.preventDefault();\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Get In Touch\n              </motion.a>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,WAAW,EAAE,WAAW,EAAmB;IACjE,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;IACnE;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG,CAAC;oDAAE;;sEAEpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FACX,IAAI,QAAQ;;;;;;8FAEf,8OAAC;oFAAG,WAAU;8FACX,IAAI,OAAO;;;;;;;;;;;;;;;;;;8EAKlB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,8IAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,8OAAC;;wFAAM,IAAI,SAAS;wFAAC;wFAAI,IAAI,OAAO;;;;;;;;;;;;;sFAGtC,8OAAC;4EAAK,WAAW,CAAC,sDAAsD,EAAE,aAAa,IAAI,IAAI,GAAG;sFAC/F,aAAa,IAAI,IAAI;;;;;;;;;;;;;;;;;;sEAK5B,8OAAC;4DAAE,WAAU;sEACV,IAAI,WAAW;;;;;;sEAGlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAA2D;;;;;;sFAGzE,8OAAC;4EAAI,WAAU;sFACZ,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;;;;;;gEAUZ,CAAC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACnC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,mBAC7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA/DtE,IAAI,EAAE;;;;;;;;;;;;;;;;kCA6EnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAIvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,MAAK;oCACL,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,gBAAgB,eAAe;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/AwardsActivities.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaTrophy, FaUsers, FaCalendarAlt, FaMedal, FaHandsHelping } from 'react-icons/fa';\nimport { Award, Activity } from '@/types';\n\ninterface AwardsActivitiesProps {\n  awards: Award[];\n  activities: Activity[];\n}\n\nexport default function AwardsActivities({ awards, activities }: AwardsActivitiesProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <section id=\"awards-activities\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Awards & Activities\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Recognition for my achievements and involvement in extracurricular activities.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Awards Section */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <FaTrophy className=\"text-2xl text-yellow-500\" />\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Awards & Recognition\n                </h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {awards.map((award, index) => (\n                  <motion.div\n                    key={award.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n                    whileHover={{ y: -3 }}\n                  >\n                    <div className=\"flex items-start gap-4\">\n                      <div className=\"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <FaMedal className=\"text-yellow-600 dark:text-yellow-400 text-xl\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\n                          <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                            {award.title}\n                          </h4>\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{award.date}</span>\n                          </div>\n                        </div>\n                        \n                        <p className=\"text-indigo-600 dark:text-indigo-400 font-medium mb-2\">\n                          {award.organization}\n                        </p>\n                        \n                        <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                          {award.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Activities Section */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <FaUsers className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Extracurricular Activities\n                </h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {activities.map((activity, index) => (\n                  <motion.div\n                    key={activity.id}\n                    initial={{ opacity: 0, x: 20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow\"\n                    whileHover={{ y: -3 }}\n                  >\n                    <div className=\"flex items-start gap-4\">\n                      <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <FaHandsHelping className=\"text-indigo-600 dark:text-indigo-400 text-xl\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\n                          <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                            {activity.title}\n                          </h4>\n                          <div className=\"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300\">\n                            <FaCalendarAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                            <span>{activity.startDate} - {activity.endDate || 'Present'}</span>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex flex-col md:flex-row md:items-center gap-2 mb-3\">\n                          <p className=\"text-indigo-600 dark:text-indigo-400 font-medium\">\n                            {activity.organization}\n                          </p>\n                          <span className=\"hidden md:block text-gray-400\">•</span>\n                          <span className=\"px-3 py-1 bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full text-xs font-medium\">\n                            {activity.role}\n                          </span>\n                        </div>\n                        \n                        <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                          {activity.description}\n                        </p>\n\n                        {/* Current activity indicator */}\n                        {(!activity.endDate || \n                          activity.endDate.toLowerCase().includes('present') || \n                          activity.endDate.toLowerCase().includes('current')) && (\n                          <div className=\"flex items-center gap-2 text-sm mt-3\">\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                            <span className=\"text-green-600 dark:text-green-400 font-medium\">\n                              Currently active\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Summary Stats */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16\"\n          >\n            <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white text-center mb-8\">\n                Achievement Summary\n              </h3>\n              \n              <div className=\"grid md:grid-cols-4 gap-6 text-center\">\n                <div>\n                  <div className=\"w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaTrophy className=\"text-2xl text-yellow-600 dark:text-yellow-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {awards.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Awards Received\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaUsers className=\"text-2xl text-indigo-600 dark:text-indigo-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Active Involvements\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaHandsHelping className=\"text-2xl text-green-600 dark:text-green-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.filter(a => a.role.toLowerCase().includes('volunteer') || \n                                           a.title.toLowerCase().includes('volunteer')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Volunteer Roles\n                  </div>\n                </div>\n                \n                <div>\n                  <div className=\"w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FaMedal className=\"text-2xl text-purple-600 dark:text-purple-400\" />\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {activities.filter(a => a.role.toLowerCase().includes('president') || \n                                           a.role.toLowerCase().includes('leader')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Leadership Positions\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,iBAAiB,EAAE,MAAM,EAAE,UAAU,EAAyB;IACpF,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAoB,WAAU;kBACxC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;kDAKnE,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;gDACV,YAAY;oDAAE,GAAG,CAAC;gDAAE;0DAEpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAGrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,MAAM,KAAK;;;;;;sFAEd,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,8IAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,8OAAC;8FAAM,MAAM,IAAI;;;;;;;;;;;;;;;;;;8EAIrB,8OAAC;oEAAE,WAAU;8EACV,MAAM,YAAY;;;;;;8EAGrB,8OAAC;oEAAE,WAAU;8EACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;+CA5BnB,MAAM,EAAE;;;;;;;;;;;;;;;;0CAsCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;;;;;;;kDAKnE,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,WAAU;gDACV,YAAY;oDAAE,GAAG,CAAC;gDAAE;0DAEpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAG5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,SAAS,KAAK;;;;;;sFAEjB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,8IAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,8OAAC;;wFAAM,SAAS,SAAS;wFAAC;wFAAI,SAAS,OAAO,IAAI;;;;;;;;;;;;;;;;;;;8EAItD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFACV,SAAS,YAAY;;;;;;sFAExB,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;sFAChD,8OAAC;4EAAK,WAAU;sFACb,SAAS,IAAI;;;;;;;;;;;;8EAIlB,8OAAC;oEAAE,WAAU;8EACV,SAAS,WAAW;;;;;;gEAItB,CAAC,CAAC,SAAS,OAAO,IACjB,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,cACxC,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,mBAClD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAiD;;;;;;;;;;;;;;;;;;;;;;;;+CA3CpE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAyD1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAIlF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM;;;;;;8DAEhB,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,8OAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC/B,EAAE,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,MAAM;;;;;;8DAE5E,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAK5D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,8OAAC;oDAAI,WAAU;8DACZ,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC/B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,MAAM;;;;;;8DAExE,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E", "debugId": null}}, {"offset": {"line": 3185, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaGithub, FaLinkedin, FaTwitter, FaGlobe } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface ContactProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Contact({ personalInfo }: ContactProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Get In Touch\n            </h2>\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6\"></div>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              I'm always open to discussing new opportunities, collaborations, or just having a chat about technology.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Information */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Contact Information\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  <motion.div\n                    className=\"flex items-center gap-4\"\n                    whileHover={{ x: 5 }}\n                  >\n                    <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                      <FaEnvelope className=\"text-indigo-600 dark:text-indigo-400\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-300\">Email</p>\n                      <a\n                        href={`mailto:${personalInfo.email}`}\n                        className=\"text-gray-900 dark:text-white font-medium hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                      >\n                        {personalInfo.email}\n                      </a>\n                    </div>\n                  </motion.div>\n\n                  {personalInfo.phone && (\n                    <motion.div\n                      className=\"flex items-center gap-4\"\n                      whileHover={{ x: 5 }}\n                    >\n                      <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                        <FaPhone className=\"text-indigo-600 dark:text-indigo-400\" />\n                      </div>\n                      <div>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-300\">Phone</p>\n                        <a\n                          href={`tel:${personalInfo.phone}`}\n                          className=\"text-gray-900 dark:text-white font-medium hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors\"\n                        >\n                          {personalInfo.phone}\n                        </a>\n                      </div>\n                    </motion.div>\n                  )}\n\n                  <motion.div\n                    className=\"flex items-center gap-4\"\n                    whileHover={{ x: 5 }}\n                  >\n                    <div className=\"w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center\">\n                      <FaMapMarkerAlt className=\"text-indigo-600 dark:text-indigo-400\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-300\">Location</p>\n                      <p className=\"text-gray-900 dark:text-white font-medium\">\n                        {personalInfo.location}\n                      </p>\n                    </div>\n                  </motion.div>\n                </div>\n\n                {/* Social Links */}\n                <div className=\"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700\">\n                  <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                    Connect with me\n                  </h4>\n                  <div className=\"flex gap-4\">\n                    {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n                      if (!url) return null;\n                      const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n                      if (!IconComponent) return null;\n\n                      return (\n                        <motion.a\n                          key={platform}\n                          href={url}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-indigo-600 hover:text-white transition-colors\"\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.9 }}\n                        >\n                          <IconComponent size={20} />\n                        </motion.a>\n                      );\n                    })}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg\">\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n                  Send me a message\n                </h3>\n                \n                <form className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                        placeholder=\"Your name\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        Email\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Subject\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                      placeholder=\"What's this about?\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Message\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      rows={6}\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none\"\n                      placeholder=\"Tell me about your project or opportunity...\"\n                    ></textarea>\n                  </div>\n                  \n                  <motion.button\n                    type=\"submit\"\n                    className=\"w-full bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-lg font-medium transition-colors\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    Send Message\n                  </motion.button>\n                </form>\n                \n                <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-4 text-center\">\n                  Or email me directly at{' '}\n                  <a\n                    href={`mailto:${personalInfo.email}`}\n                    className=\"text-indigo-600 dark:text-indigo-400 hover:underline\"\n                  >\n                    {personalInfo.email}\n                  </a>\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,QAAQ,EAAE,YAAY,EAAgB;IAC5D,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ,8IAAA,CAAA,WAAQ;QAChB,UAAU,8IAAA,CAAA,aAAU;QACpB,SAAS,8IAAA,CAAA,YAAS;QAClB,SAAS,8IAAA,CAAA,UAAO;IAClB;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAEpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oEACpC,WAAU;8EAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;gDAKxB,aAAa,KAAK,kBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;oEACjC,WAAU;8EAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;8DAM3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;;sEAEnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAE5B,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EACV,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAO9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,8OAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wDAC5D,IAAI,CAAC,KAAK,OAAO;wDACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wDACvE,IAAI,CAAC,eAAe,OAAO;wDAE3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DAEP,MAAM;4DACN,QAAO;4DACP,KAAI;4DACJ,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;sEAEvB,cAAA,8OAAC;gEAAc,MAAM;;;;;;2DARhB;;;;;oDAWX;;;;;;;;;;;;;;;;;;;;;;;0CAOR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAItE,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAO,WAAU;8EAAkE;;;;;;8EAGlG,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAIhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAQ,WAAU;8EAAkE;;;;;;8EAGnG,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAkE;;;;;;sEAGrG,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAkE;;;;;;sEAGrG,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,MAAK;oDACL,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DACzB;;;;;;;;;;;;sDAKH,8OAAC;4CAAE,WAAU;;gDAA4D;gDAC/C;8DACxB,8OAAC;oDACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;oDACpC,WAAU;8DAET,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}, {"offset": {"line": 3732, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaHeart, FaGithub, FaLinkedin, FaTwitter, FaGlobe, FaArrowUp } from 'react-icons/fa';\nimport { PersonalInfo } from '@/types';\n\ninterface FooterProps {\n  personalInfo: PersonalInfo;\n}\n\nexport default function Footer({ personalInfo }: FooterProps) {\n  const socialIcons = {\n    github: FaGithub,\n    linkedin: FaLinkedin,\n    twitter: FaTwitter,\n    website: FaGlobe\n  };\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <footer className=\"bg-gray-900 dark:bg-black text-white py-12\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"grid md:grid-cols-3 gap-8 mb-8\">\n          {/* About Section */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">{personalInfo.name}</h3>\n            <p className=\"text-gray-300 mb-4 leading-relaxed\">\n              {personalInfo.title} passionate about creating innovative solutions \n              and contributing to meaningful projects.\n            </p>\n            <div className=\"flex gap-4\">\n              {Object.entries(personalInfo.socialLinks).map(([platform, url]) => {\n                if (!url) return null;\n                const IconComponent = socialIcons[platform as keyof typeof socialIcons];\n                if (!IconComponent) return null;\n\n                return (\n                  <motion.a\n                    key={platform}\n                    href={url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-300 hover:bg-indigo-600 hover:text-white transition-colors\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                  >\n                    <IconComponent size={18} />\n                  </motion.a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">Quick Links</h3>\n            <div className=\"space-y-2\">\n              {[\n                { name: 'About', href: '#about' },\n                { name: 'Projects', href: '#projects' },\n                { name: 'Experience', href: '#experience' },\n                { name: 'Contact', href: '#contact' },\n              ].map((link) => (\n                <motion.button\n                  key={link.name}\n                  onClick={() => {\n                    const element = document.getElementById(link.href.substring(1));\n                    element?.scrollIntoView({ behavior: 'smooth' });\n                  }}\n                  className=\"block text-gray-300 hover:text-indigo-400 transition-colors\"\n                  whileHover={{ x: 5 }}\n                >\n                  {link.name}\n                </motion.button>\n              ))}\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold mb-4\">Get In Touch</h3>\n            <div className=\"space-y-2 text-gray-300\">\n              <p>\n                <a\n                  href={`mailto:${personalInfo.email}`}\n                  className=\"hover:text-indigo-400 transition-colors\"\n                >\n                  {personalInfo.email}\n                </a>\n              </p>\n              {personalInfo.phone && (\n                <p>\n                  <a\n                    href={`tel:${personalInfo.phone}`}\n                    className=\"hover:text-indigo-400 transition-colors\"\n                  >\n                    {personalInfo.phone}\n                  </a>\n                </p>\n              )}\n              <p>{personalInfo.location}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"flex items-center gap-2 text-gray-300 mb-4 md:mb-0\">\n              <span>Made with</span>\n              <motion.div\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                <FaHeart className=\"text-red-500\" />\n              </motion.div>\n              <span>using Next.js, TypeScript & Tailwind CSS</span>\n            </div>\n\n            <motion.button\n              onClick={scrollToTop}\n              className=\"flex items-center gap-2 text-gray-300 hover:text-indigo-400 transition-colors\"\n              whileHover={{ y: -2 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span>Back to top</span>\n              <FaArrowUp />\n            </motion.button>\n          </div>\n\n          <div className=\"text-center text-gray-400 text-sm mt-4\">\n            <p>&copy; {new Date().getFullYear()} {personalInfo.name}. All rights reserved.</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,OAAO,EAAE,YAAY,EAAe;IAC1D,MAAM,cAAc;QAClB,QAAQ,8IAAA,CAAA,WAAQ;QAChB,UAAU,8IAAA,CAAA,aAAU;QACpB,SAAS,8IAAA,CAAA,YAAS;QAClB,SAAS,8IAAA,CAAA,UAAO;IAClB;IAEA,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B,aAAa,IAAI;;;;;;8CACzD,8OAAC;oCAAE,WAAU;;wCACV,aAAa,KAAK;wCAAC;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,aAAa,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI;wCAC5D,IAAI,CAAC,KAAK,OAAO;wCACjB,MAAM,gBAAgB,WAAW,CAAC,SAAqC;wCACvE,IAAI,CAAC,eAAe,OAAO;wCAE3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM;4CACN,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDAEvB,cAAA,8OAAC;gDAAc,MAAM;;;;;;2CARhB;;;;;oCAWX;;;;;;;;;;;;sCAKJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAS,MAAM;wCAAS;wCAChC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;wCACtC;4CAAE,MAAM;4CAAc,MAAM;wCAAc;wCAC1C;4CAAE,MAAM;4CAAW,MAAM;wCAAW;qCACrC,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,SAAS;gDACP,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC;gDAC5D,SAAS,eAAe;oDAAE,UAAU;gDAAS;4CAC/C;4CACA,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAE;sDAElB,KAAK,IAAI;2CARL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAetB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;gDACpC,WAAU;0DAET,aAAa,KAAK;;;;;;;;;;;wCAGtB,aAAa,KAAK,kBACjB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,CAAC,IAAI,EAAE,aAAa,KAAK,EAAE;gDACjC,WAAU;0DAET,aAAa,KAAK;;;;;;;;;;;sDAIzB,8OAAC;sDAAG,aAAa,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;sDAE5C,cAAA,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC;sDAAK;;;;;;sDACN,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;oCAAE;oCAAQ,IAAI,OAAO,WAAW;oCAAG;oCAAE,aAAa,IAAI;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}]}